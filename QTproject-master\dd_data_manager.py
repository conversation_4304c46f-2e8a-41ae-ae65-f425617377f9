import pandas as pd
from datetime import datetime
import os


class DDDataManager:
    def __init__(self):
        self.priorities = {}

    def setPriorities(self, priorities):
        self.priorities = priorities

    def savePrioritiesToExcel(self):
        """保存发射优先级到Excel并返回保存路径，失败返回None"""
        if not self.priorities and self.option_count == 0:
            return None

        # 计算未设置优先级的值
        if not self.priorities:
            # 所有选项都未设置，优先级为1
            default_priority = 1
        else:
            # 已设置优先级的最大值加1
            max_priority = max(self.priorities.values())
            default_priority = max_priority + 1

        # 创建数据框
        data = []
        for option_id in range(1, self.option_count + 1):
            priority = self.priorities.get(option_id, default_priority)
            data.append({
                "选项编号": option_id,
                "优先级": priority
            })

        df = pd.DataFrame(data)

        # 确保保存目录存在
        save_dir = os.path.join(os.getcwd(), "data")
        os.makedirs(save_dir, exist_ok=True)

        # 保存到Excel
        filename = f"DD发射优先级_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path = os.path.join(save_dir, filename)

        try:
            df.to_excel(file_path, index=False)
            return file_path
        except Exception as e:
            print(f"保存Excel失败: {e}")
            return None