#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
import datetime

class FinalDuplicateFixVerification(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终重复图表修复验证")
        # 设置1800x1000界面尺寸
        self.setGeometry(50, 50, 1800, 1000)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        self.setup_verification()
        
    def setup_verification(self):
        """设置验证界面"""
        parent = self.centralWidget()
        
        # 验证报告
        verification_report = QLabel(f"""🎉 重复图表问题修复验证报告

问题: 界面底部出现注水/吹除平衡图表的重复显示

✅ 修复措施已实施:

1. 父窗口设置修复:
   • 原来: WaterBalanceChart(self.Setparameter)
   • 修复: WaterBalanceChart(self) 
   • 结果: 图表正确归属于主窗口

2. 旧图表强力清理:
   • 添加 hide() 隐藏
   • 添加 close() 关闭
   • 添加 deleteLater() 延迟删除
   • 添加 delattr() 移除属性引用

3. 图表尺寸限制移除:
   • 移除 setMinimumSize(400, 350)
   • 移除 setMaximumSize(500, 450)
   • 允许图表动态调整尺寸

4. 界面尺寸优化:
   • 界面宽度: 1600 → 1800 像素
   • 图表位置: (1200, 500, 550, 450)
   • 充足的显示空间

✅ 预期结果:
• 只有一个注水/吹除平衡图表
• 图表位置在右下角 (1200, 500)
• 图表尺寸 550×450 像素
• 界面底部无残留图表

✅ 验证方法:
1. 启动主程序 main.py
2. 检查控制台输出只有一条图表创建消息
3. 检查界面右下角有完整图表
4. 检查界面底部无残留部分

如果仍有问题，可能需要:
• 完全重启Python进程
• 检查是否有其他测试程序在运行
• 确认所有matplotlib缓存已清理""", parent)
        
        verification_report.setGeometry(50, 50, 800, 600)
        verification_report.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        verification_report.setStyleSheet("""
            background-color: rgba(0, 40, 0, 0.8);
            color: white;
            font-size: 11px;
            border: 2px solid #00FF00;
            border-radius: 5px;
            padding: 15px;
        """)
        verification_report.setWordWrap(True)
        
        # 正确图表位置标记
        correct_position = QLabel("✅ 正确位置\n注水/吹除平衡图表\n(1200, 500, 550, 450)", parent)
        correct_position.setGeometry(1200, 500, 550, 450)
        correct_position.setAlignment(Qt.AlignCenter)
        correct_position.setStyleSheet("""
            background-color: rgba(0, 255, 0, 0.2);
            border: 3px solid #00FF00;
            color: #00FF00;
            font-size: 18px;
            font-weight: bold;
        """)
        
        # 底部检查区域
        bottom_check = QLabel("🔍 检查区域: 此处应该没有任何图表残留", parent)
        bottom_check.setGeometry(50, 960, 1700, 30)
        bottom_check.setAlignment(Qt.AlignCenter)
        bottom_check.setStyleSheet("""
            background-color: rgba(255, 255, 0, 0.3);
            border: 2px dashed #FFFF00;
            color: #FFFF00;
            font-size: 14px;
            font-weight: bold;
        """)
        
        # 状态指示
        status_indicator = QLabel("🟢 修复状态: 已完成", parent)
        status_indicator.setGeometry(900, 100, 300, 50)
        status_indicator.setAlignment(Qt.AlignCenter)
        status_indicator.setStyleSheet("""
            background-color: rgba(0, 100, 0, 0.8);
            border: 2px solid #00FF00;
            border-radius: 10px;
            color: #00FF00;
            font-size: 16px;
            font-weight: bold;
        """)
        
        # 测试按钮
        test_main_btn = QPushButton("🚀 启动主程序测试", parent)
        test_main_btn.setGeometry(900, 200, 200, 50)
        test_main_btn.clicked.connect(self.launch_main_program)
        
        # 时间显示
        self.time_label = QLabel(parent)
        self.time_label.setGeometry(1600, 20, 180, 30)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("color: white; font-size: 14px; font-weight: bold;")
        
        # 设置时间更新
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"验证时间: {current_time}")
        
    def launch_main_program(self):
        """启动主程序进行测试"""
        import subprocess
        try:
            subprocess.Popen([sys.executable, "main.py"], cwd=".")
            print("主程序已启动，请检查是否还有重复图表")
        except Exception as e:
            print(f"启动主程序失败: {e}")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = FinalDuplicateFixVerification()
    window.show()
    
    print("🎉 重复图表修复验证程序启动")
    print("✅ 修复措施已实施:")
    print("   • 父窗口设置修复")
    print("   • 旧图表强力清理")
    print("   • 图表尺寸限制移除")
    print("   • 界面尺寸优化")
    print("\n请检查主程序是否还有底部残留图表")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
