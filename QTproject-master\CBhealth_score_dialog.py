from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QRadioButton, QButtonGroup,
                             QDialogButtonBox, QScrollArea, QWidget)


class CBHealthScoreDialog(QDialog):
    def __init__(self, parent=None, option_count=0):
        super().__init__(parent)
        self.setWindowTitle('设置船舶健康分')
        self.resize(600, 500)  # 增大初始尺寸
        self.option_count = option_count
        self.scores = {}  # {option_id: {'pipeline': score, 'circuit': score}}

        self.setupUI()

    def setupUI(self):
        main_layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)

        # 创建滚动区域内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        if self.option_count <= 0:
            scroll_layout.addWidget(QLabel("请设置有效的选项数量", self))
        else:
            for i in range(self.option_count):
                option_id = i + 1
                group_layout = QVBoxLayout()

                # 添加选项标题
                title_label = QLabel(f"船舶 {option_id}:", self)
                title_label.setStyleSheet("font-weight: bold;")
                group_layout.addWidget(title_label)

                # 管路健康分行
                pipeline_layout = QHBoxLayout()
                pipeline_layout.addWidget(QLabel("管路健康分:", self))

                pipeline_group = QButtonGroup(self)
                for score in [0, 50, 100]:
                    radio = QRadioButton(str(score), self)
                    radio.setProperty("option_id", option_id)
                    radio.setProperty("system_type", "pipeline")
                    radio.setProperty("score", score)
                    pipeline_group.addButton(radio, score)
                    pipeline_layout.addWidget(radio)

                    # 默认选择100分
                    if score == 100:
                        radio.setChecked(True)

                    # 连接信号
                    radio.toggled.connect(self.on_score_selected)

                group_layout.addLayout(pipeline_layout)

                # 电路健康分行
                circuit_layout = QHBoxLayout()
                circuit_layout.addWidget(QLabel("电路健康分:", self))

                circuit_group = QButtonGroup(self)
                for score in [0, 50, 100]:
                    radio = QRadioButton(str(score), self)
                    radio.setProperty("option_id", option_id)
                    radio.setProperty("system_type", "circuit")
                    radio.setProperty("score", score)
                    circuit_group.addButton(radio, score)
                    circuit_layout.addWidget(radio)

                    # 默认选择100分
                    if score == 100:
                        radio.setChecked(True)

                    # 连接信号
                    radio.toggled.connect(self.on_score_selected)

                group_layout.addLayout(circuit_layout)

                # 添加分隔线
                line = QLabel(self)
                line.setFrameShape(QLabel.HLine)
                line.setFrameShadow(QLabel.Sunken)
                group_layout.addWidget(line)

                scroll_layout.addLayout(group_layout)

        # 设置滚动区域内容
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # 添加确认和取消按钮
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = btn_box.button(QDialogButtonBox.Ok)
        cancel_button = btn_box.button(QDialogButtonBox.Cancel)
        ok_button.setText("确定")
        cancel_button.setText("取消")

        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        main_layout.addWidget(btn_box)

    def on_score_selected(self, checked):
        radio = self.sender()
        if checked:
            option_id = radio.property("option_id")
            system_type = radio.property("system_type")
            score = radio.property("score")

            if option_id not in self.scores:
                self.scores[option_id] = {}

            self.scores[option_id][system_type] = score

    def getScores(self):
        """返回所有设置的分数"""
        # 确保每个选项都有完整的分数
        for option_id in range(1, self.option_count + 1):
            if option_id not in self.scores:
                self.scores[option_id] = {'pipeline': 100, 'circuit': 100}

            # 确保每个系统都有分数
            if 'pipeline' not in self.scores[option_id]:
                self.scores[option_id]['pipeline'] = 100
            if 'circuit' not in self.scores[option_id]:
                self.scores[option_id]['circuit'] = 100

        return self.scores