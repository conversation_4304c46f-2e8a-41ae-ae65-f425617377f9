import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QLabel, QGroupBox
from PyQt5.QtGui import QPainter, QPen, QBrush
from PyQt5.QtCore import Qt


class CircleDrawer(QWidget):
    def __init__(self):
        super().__init__()
        self.num_circles = 0
        self.initUI()

    def initUI(self):
        # 创建一个QGroupBox
        group_box = QGroupBox("圆绘制设置", self)

        # 创建输入框和标签
        input_label = QLabel("请输入圆的数量:", group_box)
        self.input_box = QLineEdit(group_box)
        self.input_box.textChanged.connect(self.update_circles)

        # 设置QGroupBox的布局
        group_layout = QVBoxLayout()
        group_layout.addWidget(input_label)
        group_layout.addWidget(self.input_box)
        group_box.setLayout(group_layout)

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(group_box)

        self.setLayout(main_layout)
        self.setGeometry(300, 300, 800, 400)
        self.setWindowTitle('圆绘制器')
        self.show()

    def update_circles(self, text):
        try:
            # 尝试将输入的文本转换为整数
            self.num_circles = int(text)
        except ValueError:
            self.num_circles = 0
        # 重绘界面
        self.update()

    def paintEvent(self, event):
        qp = QPainter()
        qp.begin(self)
        self.draw_circles(qp)
        qp.end()

    def draw_circles(self, qp):
        # 设置画笔和画刷
        pen = QPen(Qt.black, 2, Qt.SolidLine)
        qp.setPen(pen)
        brush = QBrush(Qt.gray, Qt.SolidPattern)
        qp.setBrush(brush)

        # 每行最大圆的数量
        max_per_row = (self.num_circles + 1) // 2
        # 圆的半径
        radius = 20
        # 圆之间的间距
        spacing = 10
        # 起始坐标
        start_x = 50
        start_y = 150

        for i in range(self.num_circles):
            # 计算当前圆所在的行和列
            row = i // max_per_row
            col = i % max_per_row
            # 计算当前圆的圆心坐标
            x = start_x + col * (2 * radius + spacing)
            y = start_y + row * (2 * radius + spacing)
            # 绘制圆
            qp.drawEllipse(x, y, 2 * radius, 2 * radius)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    cd = CircleDrawer()
    sys.exit(app.exec_())