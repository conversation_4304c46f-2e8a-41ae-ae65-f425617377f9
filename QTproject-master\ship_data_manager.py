import pandas as pd
from datetime import datetime
import os


class ShipDataManager:
    def __init__(self):
        self.scores = {}

    def setScores(self, scores):
        self.scores = scores

    def saveScoresToExcel(self):
        """保存健康分到Excel并返回保存路径，失败返回None"""
        if not self.scores:
            return None

        # 创建数据框
        data = []
        for option_id in sorted(self.scores.keys()):
            ship_scores = self.scores.get(option_id, {})
            pipeline_score = ship_scores.get('pipeline', 50)
            circuit_score = ship_scores.get('circuit', 50)

            data.append({
                "船舶编号": option_id,
                "管路健康分": pipeline_score,
                "电路健康分": circuit_score
            })

        df = pd.DataFrame(data)

        # 确保保存目录存在
        save_dir = os.path.join(os.getcwd(), "data")
        os.makedirs(save_dir, exist_ok=True)

        # 保存到Excel
        filename = f"船舶健康分_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path = os.path.join(save_dir, filename)

        try:
            df.to_excel(file_path, index=False)
            return file_path
        except Exception as e:
            print(f"保存Excel失败: {e}")
            return None