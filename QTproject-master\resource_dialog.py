from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QComboBox, QDialogButtonBox,
                             QScrollArea, QWidget, QMessageBox)
from PyQt5.QtCore import Qt


class ResourceDialog(QDialog):
    def __init__(self, parent=None, option_count=0):
        super().__init__(parent)
        self.setWindowTitle('气源/水源/液压源选择')
        self.resize(600, 500)
        self.option_count = option_count
        self.resources = {}  # {option_id: {'gas': value, 'water': value, 'hydraulic': value}}

        self.setupUI()

    def setupUI(self):
        main_layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)

        # 创建滚动区域内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        if self.option_count <= 0:
            scroll_layout.addWidget(QLabel("请设置有效的选项数量", self))
        else:
            # 添加表头
            header_layout = QHBoxLayout()
            header_layout.addWidget(QLabel("DD", self), 1)
            header_layout.addWidget(QLabel("气源", self), 3)
            header_layout.addWidget(QLabel("水源", self), 3)
            header_layout.addWidget(QLabel("液压源", self), 3)
            scroll_layout.addLayout(header_layout)

            # 添加分隔线
            line = QLabel(self)
            line.setFrameShape(QLabel.HLine)
            line.setFrameShadow(QLabel.Sunken)
            scroll_layout.addWidget(line)

            for i in range(self.option_count):
                option_id = i + 1
                row_layout = QHBoxLayout()

                # 添加DD编号
                row_layout.addWidget(QLabel(f"{option_id}", self), 1)

                # 气源下拉框
                gas_layout = QHBoxLayout()
                gas_layout.addWidget(QLabel("气源:", self))
                gas_combo = QComboBox(self)
                gas_combo.addItems(["OK", "FAIL"])
                gas_combo.setCurrentText("OK")  # 默认值为OK
                gas_combo.setProperty("option_id", option_id)
                gas_combo.setProperty("resource_type", "gas")
                gas_combo.currentTextChanged.connect(self.on_combo_changed)
                gas_combo.setStyleSheet("QComboBox { background-color: #90EE90; }")  # 默认绿色
                gas_layout.addWidget(gas_combo)
                row_layout.addLayout(gas_layout, 3)

                # 水源下拉框
                water_layout = QHBoxLayout()
                water_layout.addWidget(QLabel("水源:", self))
                water_combo = QComboBox(self)
                water_combo.addItems(["OK", "FAIL"])
                water_combo.setCurrentText("OK")  # 默认值为OK
                water_combo.setProperty("option_id", option_id)
                water_combo.setProperty("resource_type", "water")
                water_combo.currentTextChanged.connect(self.on_combo_changed)
                water_combo.setStyleSheet("QComboBox { background-color: #90EE90; }")  # 默认绿色
                water_layout.addWidget(water_combo)
                row_layout.addLayout(water_layout, 3)

                # 液压源下拉框
                hydraulic_layout = QHBoxLayout()
                hydraulic_layout.addWidget(QLabel("液压源:", self))
                hydraulic_combo = QComboBox(self)
                hydraulic_combo.addItems(["OK", "FAIL"])
                hydraulic_combo.setCurrentText("OK")  # 默认值为OK
                hydraulic_combo.setProperty("option_id", option_id)
                hydraulic_combo.setProperty("resource_type", "hydraulic")
                hydraulic_combo.currentTextChanged.connect(self.on_combo_changed)
                hydraulic_combo.setStyleSheet("QComboBox { background-color: #90EE90; }")  # 默认绿色
                hydraulic_layout.addWidget(hydraulic_combo)
                row_layout.addLayout(hydraulic_layout, 3)

                # 初始化资源值为OK
                if option_id not in self.resources:
                    self.resources[option_id] = {}
                self.resources[option_id]['gas'] = "OK"
                self.resources[option_id]['water'] = "OK"
                self.resources[option_id]['hydraulic'] = "OK"

                scroll_layout.addLayout(row_layout)

                # 添加分隔线
                line = QLabel(self)
                line.setFrameShape(QLabel.HLine)
                line.setFrameShadow(QLabel.Sunken)
                scroll_layout.addWidget(line)

        # 设置滚动区域内容
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # 添加确认和取消按钮
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = btn_box.button(QDialogButtonBox.Ok)
        cancel_button = btn_box.button(QDialogButtonBox.Cancel)
        ok_button.setText("确定")
        cancel_button.setText("取消")

        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        main_layout.addWidget(btn_box)

    def on_combo_changed(self):
        sender = self.sender()
        option_id = sender.property("option_id")
        resource_type = sender.property("resource_type")
        value = sender.currentText()

        if option_id not in self.resources:
            self.resources[option_id] = {}

        # 保存选择的状态值
        self.resources[option_id][resource_type] = value

        # 根据状态设置颜色
        if value == "OK":
            sender.setStyleSheet("QComboBox { background-color: #90EE90; }")  # 浅绿色
        elif value == "FAIL":
            sender.setStyleSheet("QComboBox { background-color: #FFB6C1; }")  # 浅红色

    def getResources(self):
        """返回所有设置的资源值"""
        # 确保每个选项都有完整的资源值
        for option_id in range(1, self.option_count + 1):
            if option_id not in self.resources:
                self.resources[option_id] = {'gas': 'OK', 'water': 'OK', 'hydraulic': 'OK'}

            # 确保每种资源类型都有值
            for resource_type in ['gas', 'water', 'hydraulic']:
                if resource_type not in self.resources[option_id]:
                    self.resources[option_id][resource_type] = 'OK'

        return self.resources