import pandas as pd
from datetime import datetime
import os


class ResourceDataManager:
    def __init__(self):
        self.resources = {}

    def setResources(self, resources):
        self.resources = resources

    def saveToExcel(self):
        """保存资源数据到Excel并返回保存路径，失败返回None"""
        if not self.resources:
            return None

        # 创建数据框
        data = []
        for option_id in sorted(self.resources.keys()):
            resource_values = self.resources.get(option_id, {})
            gas_value = resource_values.get('gas')
            water_value = resource_values.get('water')
            hydraulic_value = resource_values.get('hydraulic')

            data.append({
                "DD": option_id,
                "气源": gas_value,
                "水源": water_value,
                "液压源": hydraulic_value
            })

        df = pd.DataFrame(data)

        # 确保保存目录存在
        save_dir = os.path.join(os.getcwd(), "data")
        os.makedirs(save_dir, exist_ok=True)

        # 保存到Excel
        filename = f"资源数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path = os.path.join(save_dir, filename)

        try:
            df.to_excel(file_path, index=False)
            return file_path
        except Exception as e:
            print(f"保存Excel失败: {e}")
            return None