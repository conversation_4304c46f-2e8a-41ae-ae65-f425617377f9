import numpy as np
import itertools
import pandas as pd
from tqdm import tqdm
import math
import os

class MissileLaunchBaseEvaluator:
    """导弹发射评估器基类，包含共用方法"""
    
    def __init__(self, num_missiles=16):
        """初始化导弹发射评估器
        
        Args:
            num_missiles: 导弹总数，可取值为4、8、12、16、32
        """
        # 确保导弹数量有效
        assert num_missiles in [4, 8, 12, 16, 32], "导弹数量必须为4、8、12、16或32"
        
        self.total_missiles = num_missiles
        self.rows = num_missiles // 2
        self.cols = 2
        self.missile_positions = self._initialize_positions()
        
    def _initialize_positions(self):
        """初始化导弹位置，返回导弹编号到位置的映射"""
        positions = {}
        for i in range(1, self.total_missiles + 1):
            # 奇数在左侧(列0)，偶数在右侧(列1)
            col = 0 if i % 2 == 1 else 1
            # 计算行号(从0开始)
            row = (i - 1) // 2
            positions[i] = (row, col)
        return positions
    
    def get_row(self, missile_id):
        """获取导弹所在行号"""
        return self.missile_positions[missile_id][0]
    
    def get_col(self, missile_id):
        """获取导弹所在列号"""
        return self.missile_positions[missile_id][1]
    
    def is_odd(self, missile_id):
        """判断导弹编号是否为奇数"""
        return missile_id % 2 == 1
    
    def euclidean_distance(self, missile1, missile2):
        """计算两枚导弹的二维欧氏距离"""
        row1, col1 = self.get_row(missile1), self.get_col(missile1)
        row2, col2 = self.get_row(missile2), self.get_col(missile2)
        
        # 根据实际物理尺寸调整行列距离权重
        # 假设行距比列距大5倍
        row_weight = 1.0
        col_weight = 5.0
        
        row_diff = (row1 - row2) * row_weight
        col_diff = (col1 - col2) * col_weight
        
        return math.sqrt(row_diff**2 + col_diff**2)

class MissileLaunchEvaluator(MissileLaunchBaseEvaluator):
    """W=3导弹发射评估器类"""
    
    def __init__(self, num_missiles=16):
        """初始化W=3导弹发射评估器"""
        super().__init__(num_missiles)
        
        # 权重参数
        self.w1 = 0.6  # 距离权重
        self.w2 = 0.4  # 交替权重
        self.w3 = 0.2  # 三发多样性权重
        
        # 非线性函数参数
        self.distance_alpha = 0.8  # 距离衰减参数
        self.alternation_beta = 1.5  # 交替奖励因子
        self.diversity_gamma = 1.2  # 多样性非线性因子
        
        # 评分分布参数 - 用于控制分数自然落在[0.9, 1]区间
        self.score_min_threshold = 0.9  # 分数下限阈值
        self.distance_k = 0.8  # 距离函数曲线参数
        self.distance_offset = -2  # 距离函数偏移
        self.alternation_penalty = 0.08  # 不交替的惩罚因子
        self.diversity_curve = 1/3  # 多样性曲线指数
        
    def distance_score(self, current, next_missile):
        """计算距离评分，使用修正的非线性函数
        
        通过参数化的sigmoid函数自然产生[阈值,1]范围的分数
        """
        row_current = self.get_row(current)
        row_next = self.get_row(next_missile)
        row_diff = abs(row_current - row_next)
        
        # 使用参数化的sigmoid函数
        base = 1 - self.score_min_threshold  # 动态计算变换范围
        score = self.score_min_threshold + base/(1 + math.exp(-(self.distance_k*row_diff + self.distance_offset)))
        
        # 归一化处理，确保最大距离接近1.0分
        max_row_diff = self.rows - 1
        max_possible_score = self.score_min_threshold + base/(1 + math.exp(-(self.distance_k*max_row_diff + self.distance_offset)))
        if max_possible_score > self.score_min_threshold:
            # 重新缩放到[阈值,1]范围
            score = self.score_min_threshold + (score - self.score_min_threshold) / (max_possible_score - self.score_min_threshold) * (1 - self.score_min_threshold)
            
        return score
    
    def side_alternation_score(self, current, next_missile):
        """计算左右交替评分，使用非线性奖励函数"""
        current_is_odd = self.is_odd(current)
        next_is_odd = self.is_odd(next_missile)
        
        if current_is_odd != next_is_odd:
            # 一左一右交替发射，满分
            return 1.0
        else:
            # 同侧发射的适度惩罚，保持分数在高区间
            return 1.0 - self.alternation_penalty
    
    def position_diversity(self, a, b, c):
        """计算三枚导弹的位置多样性，使用非线性模型"""
        # 计算三枚导弹的行差异和列差异
        rows = [self.get_row(a), self.get_row(b), self.get_row(c)]
        cols = [self.get_col(a), self.get_col(b), self.get_col(c)]
        
        # 计算三枚导弹间的两两距离
        distances = [
            self.euclidean_distance(a, b),
            self.euclidean_distance(b, c),
            self.euclidean_distance(a, c)
        ]
        
        # 使用距离的调和平均数评估空间分布
        # 调和平均数对小值更敏感，能够惩罚任何两枚导弹过于接近的情况
        if all(d > 0 for d in distances):
            harmonic_mean = 3 / sum(1/d for d in distances)
        else:
            harmonic_mean = 0
            
        # 归一化处理
        max_possible_distance = math.sqrt((self.rows-1)**2 + 25)  # 估计最大可能距离
        harmonic_score = harmonic_mean / max_possible_distance
        
        # 列多样性: 左右都有加分，但保持在0-1范围内
        col_diversity = 0.5 if len(set(cols)) > 1 else 0.1
        
        # 总体多样性评分，使用参数化的非线性变换
        base = 1 - self.score_min_threshold
        raw_diversity = harmonic_score * 0.5 + col_diversity
        scaled_diversity = self.score_min_threshold + base * (raw_diversity ** self.diversity_curve)
        
        return min(scaled_diversity, 1.0)
    
    def score(self, current, next_missile):
        """计算两枚导弹的评分"""
        d_score = self.distance_score(current, next_missile)
        s_score = self.side_alternation_score(current, next_missile)
        
        # 加权平均，确保结果在[阈值,1]之间
        return self.w1 * d_score + self.w2 * s_score
    
    def group_score(self, a, b, c):
        """计算三枚导弹组合的评分，确保结果在[阈值,1]之间"""
        # 计算连续两枚导弹的评分
        score_ab = self.score(a, b)
        score_bc = self.score(b, c)
        
        # 位置多样性评分
        diversity = self.position_diversity(a, b, c)
        
        # 各组成部分权重
        pair_weight1 = 0.4  # 第一对导弹评分权重
        pair_weight2 = 0.4  # 第二对导弹评分权重
        diversity_weight = 0.2  # 多样性评分权重
        
        # 加权平均，每项评分自然在[阈值,1]范围
        final_score = (score_ab * pair_weight1 + 
                      score_bc * pair_weight2 + 
                      diversity * diversity_weight)
        
        return final_score
    
    def compute_all_combinations(self):
        """计算所有可能的3发导弹组合及其评分"""
        missile_ids = list(range(1, self.total_missiles + 1))
        all_permutations = list(itertools.permutations(missile_ids, 3))
        
        results = []
        print(f"计算{len(all_permutations)}个可能的3发导弹组合评分...")
        
        for perm in tqdm(all_permutations):
            a, b, c = perm
            score = self.group_score(a, b, c)
            results.append((perm, score))
        
        # 按照导弹编号从小到大排序
        results.sort(key=lambda x: (x[0][0], x[0][1], x[0][2]))
        return results
    
    def save_to_excel(self, results, output_file="missile_launch_scores.xlsx"):
        """将评分结果保存到Excel文件"""
        # 创建数据框
        data = {
            "发射顺序": [f"{a}-{b}-{c}" for (a, b, c), _ in results],
            "评分": [score for _, score in results]
        }
        df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(output_file, index=False)
        print(f"结果已保存到 {output_file}")
    
    def find_optimal_launch_sequence(self, results, first_missile=None):
        """找出所有导弹的最优发射顺序 - 增强型贪心算法
        
        Args:
            results: 所有三发导弹组合及其评分的列表 [(tuple(a,b,c), score), ...]
            first_missile: 用户指定的首发导弹编号，如果为None则不限制首发导弹
            
        Returns:
            最优发射序列和总得分(未平均)
        """
        # 构建导弹ID到组合的查找表
        missile_index = self._build_missile_group_index(results)
        
        # 创建导弹使用状态记录
        n = self.total_missiles
        used_missiles = set()
        optimal_sequence = []
        total_score = 0
        
        # 如果指定了首发导弹，先将其添加到序列中
        if first_missile is not None:
            if first_missile < 1 or first_missile > n:
                print(f"警告: 指定的首发导弹 {first_missile} 无效，将忽略此限制")
            else:
                optimal_sequence.append(first_missile)
                used_missiles.add(first_missile)
                
                # 为首发导弹计算得分 - 基于与其他导弹组合的平均表现
                first_missile_scores = []
                for (a, b, c), score in results:
                    if first_missile in (a, b, c):
                        first_missile_scores.append(score)
                
                if first_missile_scores:
                    # 以该导弹参与的所有组合的平均分作为其得分贡献
                    first_score = sum(first_missile_scores) / len(first_missile_scores)
                    # 按比例添加得分（因为只使用了一枚导弹）
                    total_score += first_score / 3
                
                print(f"已指定导弹 {first_missile} 为首发导弹")
        
        # 增强型贪心算法主循环
        while len(used_missiles) < n:
            missiles_left = n - len(used_missiles)
            
            # 处理正常的三发导弹选择
            if missiles_left >= 3:
                # 使用局部视野搜索选择最佳三发组合
                best_group, best_score = self._get_best_group_with_lookahead(
                    used_missiles, results, missile_index, look_ahead=2)
                
                if best_group:
                    optimal_sequence.extend(best_group)
                    for missile in best_group:
                        used_missiles.add(missile)
                    total_score += best_score
                    continue
            
            # 处理最后不足3枚的情况
            if missiles_left == 2:
                # 找出最佳二连发
                best_pair, pair_score = self._get_best_pair(used_missiles, results, missile_index)
                if best_pair:
                    optimal_sequence.extend(best_pair)
                    for missile in best_pair:
                        used_missiles.add(missile)
                    total_score += pair_score
                    continue
            
            # 只剩一枚导弹，直接添加
            if missiles_left == 1 or len(optimal_sequence) == 0:  # 添加len(optimal_sequence)==0检查，处理初始情况
                # 如果还没有选择任何导弹，选择评分最高的导弹组合的第一枚
                if len(optimal_sequence) == 0 and first_missile is None:  # 确保未指定首发导弹时才执行此逻辑
                    best_group = None
                    best_score = -1
                    for group, score in sorted(results, key=lambda x: x[1], reverse=True):
                        if not any(m in used_missiles for m in group):
                            best_group = group
                            best_score = score
                            break
                    
                    if best_group:
                        optimal_sequence.append(best_group[0])
                        used_missiles.add(best_group[0])
                        # 只加一部分分数，因为只选了一枚导弹
                        total_score += best_score / 3
                        continue
                
                # 常规处理单枚导弹的情况
                if missiles_left > 0:  # 确保还有导弹可以选择
                    last_missile = next(m for m in range(1, n+1) if m not in used_missiles)
                    optimal_sequence.append(last_missile)
                    used_missiles.add(last_missile)
                    # 得分可以基于该导弹与其他导弹组合的平均分
                    relevant_scores = []
                    for (a, b, c), score in results:
                        if last_missile in (a, b, c):
                            relevant_scores.append(score)
                    
                    if relevant_scores:
                        avg_score = sum(relevant_scores) / len(relevant_scores)
                        total_score += avg_score
            
            # 如果没有找到任何可行组合，退出循环
            if len(optimal_sequence) == len(used_missiles) and len(used_missiles) < n:
                break
        
        # 返回原始总分，不进行平均
        return optimal_sequence, total_score
    
    def _build_missile_group_index(self, results):
        """构建从导弹ID到包含该导弹的组合的映射"""
        n = self.total_missiles
        index = {}
        for i in range(1, n+1):
            index[i] = []
        
        # 遍历所有三发组合
        for (a, b, c), score in results:
            # 将组合添加到包含的每个导弹的列表中
            index[a].append(((a, b, c), score))
            index[b].append(((a, b, c), score))
            index[c].append(((a, b, c), score))
        
        # 对每个导弹的组合按分数降序排序
        for i in range(1, n+1):
            index[i].sort(key=lambda x: x[1], reverse=True)
        
        return index
    
    def _get_best_group_with_lookahead(self, used_missiles, results, missile_index, look_ahead=2):
        """使用局部视野搜索选择最佳三发组合
        
        Args:
            used_missiles: 已使用的导弹集合
            results: 所有组合的评分结果
            missile_index: 导弹ID到组合的查找表
            look_ahead: 往前看几步
            
        Returns:
            最佳组合和其评分
        """
        # 获取前10个可能的候选组合
        candidates = []
        for (group, score) in sorted(results, key=lambda x: x[1], reverse=True):
            if not any(m in used_missiles for m in group):
                candidates.append((group, score))
                if len(candidates) >= 10:  # 只考虑前10个高分组合
                    break
        
        if not candidates:
            return None, 0
        
        # 局部视野搜索
        best_first_group = None
        best_total_score = -float('inf')
        
        for first_group, first_score in candidates:
            # 模拟选择该组合
            temp_used = used_missiles.union(set(first_group))
            total_score = first_score
            
            # 添加启发式评估值
            potential = self._estimate_future_potential(first_group, temp_used)
            total_score += potential
            
            # 如果look_ahead>0，模拟未来look_ahead步的选择
            if look_ahead > 0:
                # 递归计算未来look_ahead步的最佳选择
                next_score = self._simulate_future_selections(
                    temp_used, results, missile_index, look_ahead)
                total_score += next_score
            
            # 更新最佳选择
            if total_score > best_total_score:
                best_total_score = total_score
                best_first_group = first_group
        
        # 返回最佳的第一步选择
        if best_first_group:
            # 找到组合的实际评分
            actual_score = 0
            for (a, b, c), score in results:
                if (a, b, c) == best_first_group:
                    actual_score = score
                    break
            return best_first_group, actual_score
        
        return None, 0
    
    def _simulate_future_selections(self, used_missiles, results, missile_index, depth):
        """模拟未来几步的选择，估计总得分"""
        if depth <= 0 or len(used_missiles) >= self.total_missiles:
            return 0
        
        # 获取可能的下一步选择
        candidates = []
        for (group, score) in sorted(results, key=lambda x: x[1], reverse=True):
            if not any(m in used_missiles for m in group):
                candidates.append((group, score))
                if len(candidates) >= 5:  # 限制搜索宽度
                    break
        
        if not candidates:
            return 0
        
        # 取最高分组合进行模拟
        best_next_group, best_next_score = candidates[0]
        temp_used = used_missiles.union(set(best_next_group))
        
        # 递归计算后续步骤
        future_score = best_next_score + self._simulate_future_selections(
            temp_used, results, missile_index, depth-1)
        
        return future_score
    
    def _estimate_future_potential(self, group, used_missiles):
        """估计选择某组合后对未来可能得分的影响"""
        # 提取最后一枚导弹作为连接点
        last_missile = group[-1]
        row, col = self.get_row(last_missile), self.get_col(last_missile)
        
        # 计算未使用导弹的位置分布
        unused_missiles = [m for m in range(1, self.total_missiles + 1) 
                         if m not in used_missiles and m not in group]
        
        if not unused_missiles:  # 如果没有未使用的导弹了
            return 0
        
        unused_positions = [(self.get_row(m), self.get_col(m)) for m in unused_missiles]
        
        # 评估未来连接的可能性
        # 1. 检查有多少导弹在不同列（一左一右原则）
        different_col = sum(1 for r, c in unused_positions if c != col)
        
        # 2. 检查有多少导弹在较远行（一前一后原则）
        distant_rows = sum(1 for r, c in unused_positions 
                          if abs(r - row) >= max(1, self.rows//4))
        
        # 综合评分
        potential = 0.0
        if unused_missiles:  # 防止除零错误
            potential = 0.6 * (different_col/len(unused_missiles)) + \
                        0.4 * (distant_rows/len(unused_missiles))
        
        # 缩放系数，影响不要过大
        return potential * 0.1
    
    def _get_best_pair(self, used_missiles, results, missile_index):
        """查找最佳的两枚导弹组合"""
        n = self.total_missiles
        available_missiles = [m for m in range(1, n+1) if m not in used_missiles]
        
        if len(available_missiles) < 2:
            return None, 0
        
        best_pair = None
        best_pair_score = -1
        
        # 遍历所有可能的2发组合
        for i in range(len(available_missiles)):
            for j in range(i+1, len(available_missiles)):
                a, b = available_missiles[i], available_missiles[j]
                
                # 计算这两枚导弹的得分，基于它们在三发组合中的表现
                # 搜集两枚导弹一起出现的组合
                pair_scores = []
                for (x, y, z), score in results:
                    if (a in (x, y, z)) and (b in (x, y, z)):
                        pair_scores.append(score)
                
                if pair_scores:
                    # 使用它们在三发组合中的平均得分
                    pair_score = sum(pair_scores) / len(pair_scores)
                else:
                    # 如果没有一起出现过，直接使用我们的评分函数
                    pair_score = self.score(a, b)
                    
                    # 添加位置多样性考虑
                    row_a, col_a = self.get_row(a), self.get_col(a)
                    row_b, col_b = self.get_row(b), self.get_col(b)
                    
                    # 稍微降低二发组评分，保持与三发组的区别
                    pair_score = pair_score * 0.95
                    
                if pair_score > best_pair_score:
                    best_pair_score = pair_score
                    best_pair = (a, b)
        
        return best_pair, best_pair_score
    
    def analyze_top_scores(self, results, top_n=10):
        """分析得分最高的组合，输出详细评分过程"""
        top_results = sorted(results, key=lambda x: x[1], reverse=True)[:top_n]
        
        analysis = []
        for i, ((a, b, c), score) in enumerate(top_results):
            detail = {
                "排名": i+1,
                "组合": f"{a}-{b}-{c}",
                "总分": f"{score:.4f}",
                "A→B距离分": f"{self.distance_score(a, b):.4f}",
                "A→B交替分": f"{self.side_alternation_score(a, b):.4f}",
                "B→C距离分": f"{self.distance_score(b, c):.4f}",
                "B→C交替分": f"{self.side_alternation_score(b, c):.4f}",
                "位置多样性": f"{self.position_diversity(a, b, c):.4f}",
                "A位置": f"({self.get_row(a)},{self.get_col(a)})",
                "B位置": f"({self.get_row(b)},{self.get_col(b)})",
                "C位置": f"({self.get_row(c)},{self.get_col(c)})"
            }
            analysis.append(detail)
        
        return analysis 

class MissileLaunchW5Evaluator:
    """W=5导弹发射评估器类"""
    
    def __init__(self, w3_evaluator):
        """初始化W=5导弹发射评估器
        
        Args:
            w3_evaluator: 已初始化的W=3评估器实例
        """        
        self.w3_evaluator = w3_evaluator
        self.total_missiles = w3_evaluator.total_missiles
        
        # 加权融合的权重参数
        self.alpha1 = 0.35  # 第一个子序列权重（包含边缘导弹1）
        self.alpha2 = 0.30  # 中间子序列权重
        self.alpha3 = 0.35  # 最后一个子序列权重（包含边缘导弹5）
        
        # 非线性映射参数
        self.sigmoid_k = 6.0    # Sigmoid曲线陡度
        self.sigmoid_x0 = 0.5   # Sigmoid中心点
        self.min_score = 0.9    # 最低分数阈值
        
        # 全局特征权重
        self.global_weight = 0.2  # 全局特征在总分中的比例
    
    def compute_global_feature_score(self, missile_sequence):
        """计算五枚导弹序列的全局特征评分
        
        Args:
            missile_sequence: 五枚导弹的序列，如(2,8,1,6,3)
            
        Returns:
            全局特征评分
        """
        if len(missile_sequence) != 5:
            return 0.9  # 默认分数
            
        # 1. 位置分布多样性 - 检查行列分布
        rows = [self.w3_evaluator.get_row(m) for m in missile_sequence]
        cols = [self.w3_evaluator.get_col(m) for m in missile_sequence]
        
        # 计算行的分散程度 - 使用标准差
        row_std = np.std(rows)
        # 标准化到[0,1]区间，考虑最大可能标准差
        max_row_std = np.std([0, self.w3_evaluator.rows-1, 0, self.w3_evaluator.rows-1, 0])
        if max_row_std > 0:
            row_diversity = min(row_std / max_row_std, 1.0)
        else:
            row_diversity = 0.5
            
        # 列多样性 - 检查左右交替
        col_changes = sum(1 for i in range(1, 5) if cols[i] != cols[i-1])
        col_diversity = min(col_changes / 4, 1.0)  # 归一化
        
        # 2. 首尾导弹的距离关系
        a, e = missile_sequence[0], missile_sequence[4]
        first_last_distance = self.w3_evaluator.euclidean_distance(a, e)
        # 归一化距离评分
        max_distance = math.sqrt((self.w3_evaluator.rows-1)**2 + 25)  # 估计最大可能距离
        first_last_score = first_last_distance / max_distance if max_distance > 0 else 0.5
        
        # 3. 整体平衡性 - 左右分布
        left_count = sum(1 for m in missile_sequence if self.w3_evaluator.get_col(m) == 0)
        balance_score = 1.0 - abs(left_count/5 - 0.5)*2  # 偏离0.5越远，分数越低
        
        # 计算全局特征总分 - 加权组合
        global_score = (0.4 * row_diversity + 
                       0.3 * col_diversity + 
                       0.2 * first_last_score + 
                       0.1 * balance_score)
        
        # 映射到[0.9, 1.0]区间
        global_score = 0.9 + 0.1 * global_score
        
        return global_score
        
    def compute_w5_score(self, sequence, w3_results_dict):
        """计算W=5序列的评分，使用改进的匹配链接方法
        
        Args:
            sequence: 5枚导弹的序列，如(2,8,1,6,3)
            w3_results_dict: W=3评分结果的字典，键为三元组，值为得分
            
        Returns:
            W=5序列的综合评分
        """
        if len(sequence) != 5:
            return 0.9  # 默认分数
        
        # 提取三个子序列
        subseq1 = (sequence[0], sequence[1], sequence[2])  # 前三个导弹
        subseq2 = (sequence[1], sequence[2], sequence[3])  # 中间三个导弹
        subseq3 = (sequence[2], sequence[3], sequence[4])  # 后三个导弹
        
        # 查找子序列的W=3分数
        score1 = w3_results_dict.get(subseq1, 0.9)
        score2 = w3_results_dict.get(subseq2, 0.9)
        score3 = w3_results_dict.get(subseq3, 0.9)
        
        # 1. 加权融合子序列分数
        weighted_local_score = (self.alpha1 * score1 + 
                               self.alpha2 * score2 + 
                               self.alpha3 * score3)
        
        # 2. 计算全局特征评分
        global_score = self.compute_global_feature_score(sequence)
        
        # 3. 结合局部和全局评分
        combined_score = ((1 - self.global_weight) * weighted_local_score + 
                         self.global_weight * global_score)
        
        # 4. 非线性映射
        # 将分数映射到[min_score, 1.0]区间
        sigmoid_value = 1.0 / (1.0 + math.exp(-self.sigmoid_k * (combined_score - self.sigmoid_x0)))
        final_score = self.min_score + (1.0 - self.min_score) * sigmoid_value
        
        return final_score
    
    def is_valid_next_missile(self, current_missile, next_missile):
        """判断下一枚导弹是否是当前导弹的合理后续
        
        根据物理位置关系，排除不合理的组合：
        1. 对于中间行的导弹，排除其周围两圈的导弹
        2. 对于首尾行的导弹，排除其同侧和相邻两行的导弹
        
        Args:
            current_missile: 当前导弹编号
            next_missile: 下一枚导弹编号
            
        Returns:
            布尔值，表示是否合理
        """
        # 获取两枚导弹的位置
        current_row = self.w3_evaluator.get_row(current_missile)
        current_col = self.w3_evaluator.get_col(current_missile)
        next_row = self.w3_evaluator.get_row(next_missile)
        next_col = self.w3_evaluator.get_col(next_missile)
        
        # 计算行列差距
        row_diff = abs(current_row - next_row)
        col_diff = abs(current_col - next_col)
        
        # 判断当前导弹是否在首尾行
        is_edge_row = (current_row == 0 or current_row == self.w3_evaluator.rows - 1)
        
        if is_edge_row:
            # 首尾行导弹规则：排除同侧和相邻两行的导弹
            if col_diff == 0:  # 同侧
                return False
            if row_diff <= 2:  # 相邻两行
                return False
        else:
            # 中间行导弹规则：排除周围两圈的导弹
            if row_diff <= 2 and col_diff <= 1:  # 周围两圈范围
                return False
        
        return True
    
    def generate_valid_permutations(self, first_missile=None):
        """生成所有符合物理位置约束的五发导弹排列
        
        Args:
            first_missile: 指定的首发导弹编号，如果为None则不限制
            
        Returns:
            符合约束的五发导弹排列列表
        """
        missile_ids = list(range(1, self.total_missiles + 1))
        valid_perms = []
        
        # 如果指定了首发导弹，则固定第一个位置
        if first_missile is not None:
            first_missiles = [first_missile]
        else:
            first_missiles = missile_ids
        
        print("生成符合物理约束的导弹序列...")
        total_count = 0
        valid_count = 0
        
        # 使用递归方式生成有效排列
        def generate_sequence(sequence, used_missiles):
            nonlocal total_count, valid_count
            
            # 如果序列长度达到5，则添加到结果中
            if len(sequence) == 5:
                valid_perms.append(tuple(sequence))
                valid_count += 1
                if valid_count % 10000 == 0:
                    print(f"已找到 {valid_count} 个有效序列，总检查 {total_count} 个组合")
                return
            
            # 获取当前序列的最后一枚导弹
            current = sequence[-1] if sequence else None
            
            # 遍历所有未使用的导弹
            for next_missile in missile_ids:
                if next_missile in used_missiles:
                    continue
                
                total_count += 1
                
                # 如果是第一枚导弹，或者与前一枚导弹符合物理约束
                if current is None or self.is_valid_next_missile(current, next_missile):
                    # 添加到序列中并继续递归
                    new_sequence = sequence + [next_missile]
                    new_used = used_missiles.union({next_missile})
                    generate_sequence(new_sequence, new_used)
        
        # 对每个可能的首发导弹，生成序列
        for first in first_missiles:
            generate_sequence([first], {first})
        
        print(f"总共生成 {len(valid_perms)} 个有效序列，总检查 {total_count} 个组合")
        return valid_perms
    
    def compute_all_w5_combinations(self, w3_results, first_missile=None):
        """计算所有可能的W=5导弹组合及其评分，使用物理位置约束优化
        
        Args:
            w3_results: W=3评分结果
            first_missile: 指定的首发导弹编号，如果为None则不限制
            
        Returns:
            符合物理约束的W=5组合评分结果
        """
        # 将W=3结果转换为字典形式，便于快速查找
        w3_results_dict = {group: score for (group, score) in w3_results}
        
        # 生成符合物理约束的排列
        valid_permutations = self.generate_valid_permutations(first_missile)
        
        results = []
        print(f"计算{len(valid_permutations)}个符合物理约束的W=5导弹组合评分...")
        
        for perm in tqdm(valid_permutations):
            score = self.compute_w5_score(perm, w3_results_dict)
            results.append((perm, score))
        
        # 按评分从高到低排序
        results.sort(key=lambda x: x[1], reverse=True)
        return results
    
    def save_to_excel(self, results, output_file="missile_launch_w5_scores.xlsx"):
        """将W=5评分结果保存到Excel文件"""
        # 创建数据框
        data = {
            "发射顺序": ["-".join(str(m) for m in group) for (group, _) in results],
            "评分": [score for _, score in results]
        }
        df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(output_file, index=False)
        print(f"W=5结果已保存到 {output_file}")
    
    def find_optimal_launch_sequence(self, w5_results, first_missile=None):
        """找出所有导弹的最优发射顺序 - 使用W=5数据
        
        Args:
            w5_results: 所有W=5导弹组合及评分的列表
            first_missile: 用户指定的首发导弹编号
            
        Returns:
            最优发射序列，平均得分和组得分列表
        """
        # 构建W=5查找表
        w5_index = {}
        for (group, score) in w5_results:
            group_key = tuple(group)
            w5_index[group_key] = score
        
        # 创建导弹使用状态记录
        n = self.total_missiles
        used_missiles = set()
        optimal_sequence = []
        group_scores = []
        
        # 处理首发导弹
        if first_missile is not None:
            if 1 <= first_missile <= n:
                optimal_sequence.append(first_missile)
                used_missiles.add(first_missile)
                print(f"已指定导弹 {first_missile} 为首发导弹")
        
        # 贪心算法寻找最优序列
        while len(used_missiles) < n:
            best_group = None
            best_score = -1
            
            # 确定下一组W=5的起始点
            start_idx = len(optimal_sequence) - 4 if len(optimal_sequence) >= 4 else 0
            if start_idx < 0:
                start_idx = 0
                
            # 如果已经有序列，考虑重叠
            prefix = tuple(optimal_sequence[start_idx:]) if start_idx < len(optimal_sequence) else ()
            
            # 寻找最佳的下一组
            for (group, score) in sorted(w5_results, key=lambda x: x[1], reverse=True):
                # 检查是否与现有序列有重叠，且不包含已使用的导弹
                can_use = True
                overlap_len = min(len(prefix), len(group))
                
                # 验证重叠部分是否匹配
                if overlap_len > 0:
                    if prefix[-overlap_len:] != group[:overlap_len]:
                        can_use = False
                
                # 检查新增的导弹是否已使用
                new_missiles = group[overlap_len:]
                for m in new_missiles:
                    if m in used_missiles:
                        can_use = False
                        break
                
                if can_use and score > best_score:
                    best_group = group
                    best_score = score
            
            # 如果找到最佳组合，添加到序列中
            if best_group:
                # 确定要添加的新导弹
                if len(prefix) > 0:
                    overlap_len = min(len(prefix), len(best_group))
                    new_missiles = best_group[overlap_len:]
                else:
                    new_missiles = best_group
                    
                # 添加新导弹到序列
                for m in new_missiles:
                    if m not in used_missiles:
                        optimal_sequence.append(m)
                        used_missiles.add(m)
                
                # 记录这个W=5组的得分
                group_scores.append(best_score)
            else:
                # 如果没有找到合适的组合，选择任意未使用的导弹
                available = [m for m in range(1, n+1) if m not in used_missiles]
                if available:
                    next_missile = min(available)  # 选择编号最小的未使用导弹
                    optimal_sequence.append(next_missile)
                    used_missiles.add(next_missile)
                else:
                    break
        
        # 计算平均得分
        avg_score = sum(group_scores) / len(group_scores) if group_scores else 0.9
        
        return optimal_sequence, avg_score, group_scores

def calculate_group_scores_w3(sequence, w3_results):
    """从序列计算W=3组的得分"""
    # 创建映射，方便查询
    w3_dict = {group: score for group, score in w3_results}
    
    group_scores = []
    n = len(sequence)
    
    # 计算完整的三发组得分
    for i in range(0, n, 3):
        if i+2 < n:
            # 完整的三发组
            group = tuple(sequence[i:i+3])
            group_score = w3_dict.get(group, 0.9)
            group_scores.append(group_score)
        else:
            # 处理最后不足3发的情况
            break
    
    return group_scores

def calculate_group_scores_w5(sequence, w5_results):
    """从序列计算W=5组的得分"""
    # 创建映射，方便查询
    w5_dict = {group: score for group, score in w5_results}
    
    group_scores = []
    n = len(sequence)
    
    # 计算完整的五发组得分
    for i in range(0, n, 5):
        if i+4 < n:
            # 完整的五发组
            group = tuple(sequence[i:i+5])
            group_score = w5_dict.get(group, 0.9)
            group_scores.append(group_score)
        else:
            # 处理最后不足5发的情况
            break
    
    return group_scores

def main():
    """主函数，处理用户输入并运行评估"""
    valid_counts = [8, 12, 16, 32]
    
    while True:
        try:
            n = int(input(f"请输入导弹数量(可选{valid_counts}): "))
            if n in valid_counts:
                break
            else:
                print(f"请输入有效的导弹数量: {valid_counts}")
        except ValueError:
            print("请输入有效的数字")
    
    # 询问是否要指定首发导弹
    first_missile = None
    while True:
        specify_first = input("是否要指定首发导弹？(y/n): ").strip().lower()
        if specify_first == 'y':
            try:
                first_missile_input = input(f"请输入首发导弹编号(1-{n})，或输入0取消: ")
                first_missile = int(first_missile_input)
                if first_missile == 0:
                    first_missile = None
                    print("已取消首发导弹指定")
                    break
                elif 1 <= first_missile <= n:
                    print(f"已指定导弹{first_missile}为首发导弹")
                    break
                else:
                    print(f"请输入有效的导弹编号(1-{n})或0取消")
            except ValueError:
                print("请输入有效的数字")
        elif specify_first == 'n':
            break
        else:
            print("请输入 y 或 n")
    
    # 文件名添加首发导弹信息
    first_missile_suffix = f"_first{first_missile}" if first_missile else ""
    
    # 1. 创建W=3评估器并计算所有W=3组合
    print("\n===== 第一阶段: W=3评分计算 =====")
    w3_evaluator = MissileLaunchEvaluator(num_missiles=n)
    
    # 检查W=3结果文件是否已存在
    w3_results_file = f"missile_launch_w3_scores_n{n}.xlsx"
    if os.path.exists(w3_results_file):
        use_existing = input(f"发现已有W=3评分文件 {w3_results_file}，是否使用现有文件？(y/n): ").strip().lower()
        if use_existing == 'y':
            # 从Excel加载W=3结果
            df = pd.read_excel(w3_results_file)
            w3_results = []
            for _, row in df.iterrows():
                # 解析发射顺序字符串，如"1-2-3"
                sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
                score = row['评分']
                w3_results.append((sequence, score))
            print(f"已加载{len(w3_results)}个W=3组合的评分结果")
        else:
            # 重新计算W=3结果
            w3_results = w3_evaluator.compute_all_combinations()
            w3_evaluator.save_to_excel(w3_results, w3_results_file)
    else:
        # 计算所有W=3组合
        w3_results = w3_evaluator.compute_all_combinations()
        w3_evaluator.save_to_excel(w3_results, w3_results_file)
    
    # 2. 使用W=3数据集寻找最优序列
    print("\n===== 第二阶段: 使用W=3数据集寻找最优序列 =====")
    w3_optimal_sequence, w3_raw_score = w3_evaluator.find_optimal_launch_sequence(w3_results, first_missile)
    
    # 重新计算各组得分，确保准确性
    w3_group_scores = calculate_group_scores_w3(w3_optimal_sequence, w3_results)
    w3_avg_score = sum(w3_group_scores) / len(w3_group_scores) if w3_group_scores else 0.9
    
    # 打印W=3最优序列
    sequence_str_w3 = "-".join(str(m) for m in w3_optimal_sequence)
    print(f"\nW=3最优发射序列 ({n}发导弹):")
    print(sequence_str_w3)
    print(f"W=3平均得分: {w3_avg_score:.4f}")
    
    # 3. 创建W=5评估器并计算W=5组合
    print("\n===== 第三阶段: W=5评分计算 =====")
    w5_evaluator = MissileLaunchW5Evaluator(w3_evaluator)
    
    # 检查W=5结果文件是否已存在
    w5_results_file = f"missile_launch_w5_scores_n{n}{first_missile_suffix}.xlsx"
    if os.path.exists(w5_results_file):
        use_existing = input(f"发现已有W=5评分文件 {w5_results_file}，是否使用现有文件？(y/n): ").strip().lower()
        if use_existing == 'y':
            # 从Excel加载W=5结果
            df = pd.read_excel(w5_results_file)
            w5_results = []
            for _, row in df.iterrows():
                sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
                score = row['评分']
                w5_results.append((sequence, score))
            print(f"已加载{len(w5_results)}个W=5组合的评分结果")
        else:
            # 重新计算W=5结果
            w5_results = w5_evaluator.compute_all_w5_combinations(w3_results, first_missile)
            w5_evaluator.save_to_excel(w5_results, w5_results_file)
    else:
        # 计算所有W=5组合
        w5_results = w5_evaluator.compute_all_w5_combinations(w3_results, first_missile)
        w5_evaluator.save_to_excel(w5_results, w5_results_file)
    
    # 4. 使用W=5数据集寻找最优序列
    print("\n===== 第四阶段: 使用W=5数据集寻找最优序列 =====")
    w5_optimal_sequence, w5_avg_score, w5_group_scores = w5_evaluator.find_optimal_launch_sequence(w5_results, first_missile)
    
    # 打印W=5最优序列
    sequence_str_w5 = "-".join(str(m) for m in w5_optimal_sequence)
    print(f"\nW=5最优发射序列 ({n}发导弹):")
    print(sequence_str_w5)
    print(f"W=5平均得分: {w5_avg_score:.4f}")
    
    # 5. 结果对比与保存
    print("\n===== 结果对比 =====")
    print(f"W=3最优序列: {sequence_str_w3}")
    print(f"W=3平均得分: {w3_avg_score:.4f}")
    print(f"W=5最优序列: {sequence_str_w5}")
    print(f"W=5平均得分: {w5_avg_score:.4f}")
    
    # 保存结果到文件
    comparison_file = f"missile_launch_comparison_n{n}{first_missile_suffix}.txt"
    with open(comparison_file, "w") as f:
        f.write("===== 导弹发射序列优化结果对比 =====\n\n")
        
        # 首发导弹信息
        if first_missile:
            f.write(f"首发导弹: {first_missile}\n\n")
        else:
            f.write("首发导弹: 未指定\n\n")
            
        # W=3结果
        f.write("===== W=3最优发射序列 =====\n")
        f.write(f"序列 ({n}发导弹): {sequence_str_w3}\n")
        f.write(f"平均得分: {w3_avg_score:.4f}\n\n")
        
        # W=3的三发一组详细分解
        f.write("按三发一组的详细分解:\n")
        for i in range(0, n, 3):
            if i+2 < n:
                group = w3_optimal_sequence[i:i+3]
                group_str = "-".join(str(m) for m in group)
                if i//3 < len(w3_group_scores):
                    f.write(f"组 {i//3+1}: {group_str}, 得分: {w3_group_scores[i//3]:.4f}\n")
            else:
                # 处理最后不足3发的情况
                group = w3_optimal_sequence[i:]
                group_str = "-".join(str(m) for m in group)
                f.write(f"组 {i//3+1}: {group_str}\n")
        
        # W=5结果
        f.write("\n===== W=5最优发射序列 =====\n")
        f.write(f"序列 ({n}发导弹): {sequence_str_w5}\n")
        f.write(f"平均得分: {w5_avg_score:.4f}\n\n")
        
        # W=5的五发一组详细分解
        f.write("按五发一组的详细分解:\n")
        for i in range(0, n, 5):
            if i+4 < n:
                group = w5_optimal_sequence[i:i+5]
                group_str = "-".join(str(m) for m in group)
                if i//5 < len(w5_group_scores):
                    f.write(f"组 {i//5+1}: {group_str}, 得分: {w5_group_scores[i//5]:.4f}\n")
            else:
                # 处理最后不足5发的情况
                group = w5_optimal_sequence[i:]
                group_str = "-".join(str(m) for m in group)
                f.write(f"组 {i//5+1}: {group_str}\n")
    
    print(f"\n比较结果已保存到 {comparison_file}")

if __name__ == "__main__":
    main() 