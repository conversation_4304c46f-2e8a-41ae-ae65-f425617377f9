from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QCheckBox, QButtonGroup, QDialogButtonBox)


class SelectionDialog(QDialog):
    def __init__(self, parent=None, option_count=0):
        super().__init__(parent)
        self.setWindowTitle('DD去选')
        self.resize(300, 400)
        self.option_count = option_count
        self.selected_options = []

        self.setupUI()

    def setupUI(self):
        layout = QVBoxLayout(self)

        if self.option_count <= 0:
            layout.addWidget(QLabel("没有可用的DD选择", self))
            return

        for i in range(self.option_count):
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(f"DD编号{i + 1}:", self))

            btn_group = QButtonGroup(self)

            # 是 选项
            yes_cb = QCheckBox("是", self)
            yes_cb.setProperty("option_id", i + 1)
            btn_group.addButton(yes_cb, 1)
            row_layout.addWidget(yes_cb)

            # 不是 选项
            no_cb = QCheckBox("不是", self)
            no_cb.setProperty("option_id", i + 1)
            btn_group.addButton(no_cb, 2)
            row_layout.addWidget(no_cb)

            # 互斥逻辑
            yes_cb.toggled.connect(lambda checked, nb=no_cb: nb.setChecked(not checked))
            no_cb.toggled.connect(lambda checked, yb=yes_cb: yb.setChecked(not checked))
            no_cb.setChecked(True)  # 默认选"不是"

            layout.addLayout(row_layout)
            yes_cb.toggled.connect(self.on_yes_toggled)

        # 添加中文按钮
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = btn_box.button(QDialogButtonBox.Ok)
        cancel_button = btn_box.button(QDialogButtonBox.Cancel)
        ok_button.setText("确定")
        cancel_button.setText("取消")

        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def on_yes_toggled(self, checked):
        cb = self.sender()
        option_id = cb.property("option_id")

        if checked and option_id not in self.selected_options:
            self.selected_options.append(option_id)
        elif not checked and option_id in self.selected_options:
            self.selected_options.remove(option_id)

    def getSelectedOptions(self):
        return self.selected_options