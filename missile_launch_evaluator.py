import numpy as np
import itertools
import pandas as pd
from tqdm import tqdm
import math

class MissileLaunchEvaluator:
    def __init__(self, num_missiles=16):
        """初始化导弹发射评估器
        
        Args:
            num_missiles: 导弹总数，可取值为4、8、12、16、32
        """
        # 确保导弹数量有效
        assert num_missiles in [4, 8, 12, 16, 32], "导弹数量必须为4、8、12、16或32"
        
        self.total_missiles = num_missiles
        self.rows = num_missiles // 2
        self.cols = 2
        self.missile_positions = self._initialize_positions()
        
        # 权重参数
        self.w1 = 0.6  # 距离权重
        self.w2 = 0.4  # 交替权重
        self.w3 = 0.2  # 三发多样性权重
        
        # 非线性函数参数
        self.distance_alpha = 0.8  # 距离衰减参数
        self.alternation_beta = 1.5  # 交替奖励因子
        self.diversity_gamma = 1.2  # 多样性非线性因子
        
        # 评分分布参数 - 用于控制分数自然落在[0.9, 1]区间
        self.score_min_threshold = 0.9  # 分数下限阈值
        self.distance_k = 0.8  # 距离函数曲线参数
        self.distance_offset = -2  # 距离函数偏移
        self.alternation_penalty = 0.08  # 不交替的惩罚因子
        self.diversity_curve = 1/3  # 多样性曲线指数
        
    def _initialize_positions(self):
        """初始化导弹位置，返回导弹编号到位置的映射"""
        positions = {}
        for i in range(1, self.total_missiles + 1):
            # 奇数在左侧(列0)，偶数在右侧(列1)
            col = 0 if i % 2 == 1 else 1
            # 计算行号(从0开始)
            row = (i - 1) // 2
            positions[i] = (row, col)
        return positions
    
    def get_row(self, missile_id):
        """获取导弹所在行号"""
        return self.missile_positions[missile_id][0]
    
    def get_col(self, missile_id):
        """获取导弹所在列号"""
        return self.missile_positions[missile_id][1]
    
    def is_odd(self, missile_id):
        """判断导弹编号是否为奇数"""
        return missile_id % 2 == 1
    
    def distance_score(self, current, next_missile):
        """计算距离评分，使用修正的非线性函数
        
        通过参数化的sigmoid函数自然产生[阈值,1]范围的分数
        """
        row_current = self.get_row(current)
        row_next = self.get_row(next_missile)
        row_diff = abs(row_current - row_next)
        
        # 使用参数化的sigmoid函数
        base = 1 - self.score_min_threshold  # 动态计算变换范围
        score = self.score_min_threshold + base/(1 + math.exp(-(self.distance_k*row_diff + self.distance_offset)))
        
        # 归一化处理，确保最大距离接近1.0分
        max_row_diff = self.rows - 1
        max_possible_score = self.score_min_threshold + base/(1 + math.exp(-(self.distance_k*max_row_diff + self.distance_offset)))
        if max_possible_score > self.score_min_threshold:
            # 重新缩放到[阈值,1]范围
            score = self.score_min_threshold + (score - self.score_min_threshold) / (max_possible_score - self.score_min_threshold) * (1 - self.score_min_threshold)
            
        return score
    
    def side_alternation_score(self, current, next_missile):
        """计算左右交替评分，使用非线性奖励函数"""
        current_is_odd = self.is_odd(current)
        next_is_odd = self.is_odd(next_missile)
        
        if current_is_odd != next_is_odd:
            # 一左一右交替发射，满分
            return 1.0
        else:
            # 同侧发射的适度惩罚，保持分数在高区间
            return 1.0 - self.alternation_penalty
    
    def euclidean_distance(self, missile1, missile2):
        """计算两枚导弹的二维欧氏距离"""
        row1, col1 = self.get_row(missile1), self.get_col(missile1)
        row2, col2 = self.get_row(missile2), self.get_col(missile2)
        
        # 根据实际物理尺寸调整行列距离权重
        # 假设行距比列距大5倍
        row_weight = 1.0
        col_weight = 5.0
        
        row_diff = (row1 - row2) * row_weight
        col_diff = (col1 - col2) * col_weight
        
        return math.sqrt(row_diff**2 + col_diff**2)
    
    def position_diversity(self, a, b, c):
        """计算三枚导弹的位置多样性，使用非线性模型"""
        # 计算三枚导弹的行差异和列差异
        rows = [self.get_row(a), self.get_row(b), self.get_row(c)]
        cols = [self.get_col(a), self.get_col(b), self.get_col(c)]
        
        # 计算三枚导弹间的两两距离
        distances = [
            self.euclidean_distance(a, b),
            self.euclidean_distance(b, c),
            self.euclidean_distance(a, c)
        ]
        
        # 使用距离的调和平均数评估空间分布
        # 调和平均数对小值更敏感，能够惩罚任何两枚导弹过于接近的情况
        if all(d > 0 for d in distances):
            harmonic_mean = 3 / sum(1/d for d in distances)
        else:
            harmonic_mean = 0
            
        # 归一化处理
        max_possible_distance = math.sqrt((self.rows-1)**2 + 25)  # 估计最大可能距离
        harmonic_score = harmonic_mean / max_possible_distance
        
        # 列多样性: 左右都有加分，但保持在0-1范围内
        col_diversity = 0.5 if len(set(cols)) > 1 else 0.1
        
        # 总体多样性评分，使用参数化的非线性变换
        base = 1 - self.score_min_threshold
        raw_diversity = harmonic_score * 0.5 + col_diversity
        scaled_diversity = self.score_min_threshold + base * (raw_diversity ** self.diversity_curve)
        
        return min(scaled_diversity, 1.0)
    
    def score(self, current, next_missile):
        """计算两枚导弹的评分"""
        d_score = self.distance_score(current, next_missile)
        s_score = self.side_alternation_score(current, next_missile)
        
        # 加权平均，确保结果在[阈值,1]之间
        return self.w1 * d_score + self.w2 * s_score
    
    def group_score(self, a, b, c):
        """计算三枚导弹组合的评分，确保结果在[阈值,1]之间"""
        # 计算连续两枚导弹的评分
        score_ab = self.score(a, b)
        score_bc = self.score(b, c)
        
        # 位置多样性评分
        diversity = self.position_diversity(a, b, c)
        
        # 各组成部分权重
        pair_weight1 = 0.4  # 第一对导弹评分权重
        pair_weight2 = 0.4  # 第二对导弹评分权重
        diversity_weight = 0.2  # 多样性评分权重
        
        # 加权平均，每项评分自然在[阈值,1]范围
        final_score = (score_ab * pair_weight1 + 
                      score_bc * pair_weight2 + 
                      diversity * diversity_weight)
        
        return final_score
    
    def compute_all_combinations(self):
        """计算所有可能的3发导弹组合及其评分"""
        missile_ids = list(range(1, self.total_missiles + 1))
        all_permutations = list(itertools.permutations(missile_ids, 3))
        
        results = []
        print(f"计算{len(all_permutations)}个可能的3发导弹组合评分...")
        
        for perm in tqdm(all_permutations):
            a, b, c = perm
            score = self.group_score(a, b, c)
            results.append((perm, score))
        
        # 按照导弹编号从小到大排序
        results.sort(key=lambda x: (x[0][0], x[0][1], x[0][2]))
        return results
    
    def save_to_excel(self, results, output_file="missile_launch_scores.xlsx"):
        """将评分结果保存到Excel文件"""
        # 创建数据框
        data = {
            "发射顺序": [f"{a}-{b}-{c}" for (a, b, c), _ in results],
            "评分": [score for _, score in results]
        }
        df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(output_file, index=False)
        print(f"结果已保存到 {output_file}")
    
    def find_optimal_launch_sequence(self, results, first_missile=None):
        """找出所有导弹的最优发射顺序 - 增强型贪心算法
        
        Args:
            results: 所有三发导弹组合及其评分的列表 [(tuple(a,b,c), score), ...]
            first_missile: 用户指定的首发导弹编号，如果为None则不限制首发导弹
            
        Returns:
            最优发射序列和总得分(未平均)
        """
        # 构建导弹ID到组合的查找表
        missile_index = self._build_missile_group_index(results)
        
        # 创建导弹使用状态记录
        n = self.total_missiles
        used_missiles = set()
        optimal_sequence = []
        total_score = 0
        
        # 如果指定了首发导弹，先将其添加到序列中
        if first_missile is not None:
            if first_missile < 1 or first_missile > n:
                print(f"警告: 指定的首发导弹 {first_missile} 无效，将忽略此限制")
            else:
                optimal_sequence.append(first_missile)
                used_missiles.add(first_missile)
                
                # 为首发导弹计算得分 - 基于与其他导弹组合的平均表现
                first_missile_scores = []
                for (a, b, c), score in results:
                    if first_missile in (a, b, c):
                        first_missile_scores.append(score)
                
                if first_missile_scores:
                    # 以该导弹参与的所有组合的平均分作为其得分贡献
                    first_score = sum(first_missile_scores) / len(first_missile_scores)
                    # 按比例添加得分（因为只使用了一枚导弹）
                    total_score += first_score / 3
                
                print(f"已指定DD {first_missile} 为首发DD")
        
        # 增强型贪心算法主循环
        while len(used_missiles) < n:
            missiles_left = n - len(used_missiles)
            
            # 处理正常的三发导弹选择
            if missiles_left >= 3:
                # 使用局部视野搜索选择最佳三发组合
                best_group, best_score = self._get_best_group_with_lookahead(
                    used_missiles, results, missile_index, look_ahead=2)
                
                if best_group:
                    optimal_sequence.extend(best_group)
                    for missile in best_group:
                        used_missiles.add(missile)
                    total_score += best_score
                    continue
            
            # 处理最后不足3枚的情况
            if missiles_left == 2:
                # 找出最佳二连发
                best_pair, pair_score = self._get_best_pair(used_missiles, results, missile_index)
                if best_pair:
                    optimal_sequence.extend(best_pair)
                    for missile in best_pair:
                        used_missiles.add(missile)
                    total_score += pair_score
                    continue
            
            # 只剩一枚导弹，直接添加
            if missiles_left == 1 or len(optimal_sequence) == 0:  # 添加len(optimal_sequence)==0检查，处理初始情况
                # 如果还没有选择任何导弹，选择评分最高的导弹组合的第一枚
                if len(optimal_sequence) == 0 and first_missile is None:  # 确保未指定首发导弹时才执行此逻辑
                    best_group = None
                    best_score = -1
                    for group, score in sorted(results, key=lambda x: x[1], reverse=True):
                        if not any(m in used_missiles for m in group):
                            best_group = group
                            best_score = score
                            break
                    
                    if best_group:
                        optimal_sequence.append(best_group[0])
                        used_missiles.add(best_group[0])
                        # 只加一部分分数，因为只选了一枚导弹
                        total_score += best_score / 3
                        continue
                
                # 常规处理单枚导弹的情况
                if missiles_left > 0:  # 确保还有导弹可以选择
                    last_missile = next(m for m in range(1, n+1) if m not in used_missiles)
                    optimal_sequence.append(last_missile)
                    used_missiles.add(last_missile)
                    # 得分可以基于该导弹与其他导弹组合的平均分
                    relevant_scores = []
                    for (a, b, c), score in results:
                        if last_missile in (a, b, c):
                            relevant_scores.append(score)
                    
                    if relevant_scores:
                        avg_score = sum(relevant_scores) / len(relevant_scores)
                        total_score += avg_score
            
            # 如果没有找到任何可行组合，退出循环
            if len(optimal_sequence) == len(used_missiles) and len(used_missiles) < n:
                break
        
        # 返回原始总分，不进行平均
        return optimal_sequence, total_score
    
    def _build_missile_group_index(self, results):
        """构建从导弹ID到包含该导弹的组合的映射"""
        n = self.total_missiles
        index = {}
        for i in range(1, n+1):
            index[i] = []
        
        # 遍历所有三发组合
        for (a, b, c), score in results:
            # 将组合添加到包含的每个导弹的列表中
            index[a].append(((a, b, c), score))
            index[b].append(((a, b, c), score))
            index[c].append(((a, b, c), score))
        
        # 对每个导弹的组合按分数降序排序
        for i in range(1, n+1):
            index[i].sort(key=lambda x: x[1], reverse=True)
        
        return index
    
    def _get_best_group_with_lookahead(self, used_missiles, results, missile_index, look_ahead=2):
        """使用局部视野搜索选择最佳三发组合
        
        Args:
            used_missiles: 已使用的导弹集合
            results: 所有组合的评分结果
            missile_index: 导弹ID到组合的查找表
            look_ahead: 往前看几步
            
        Returns:
            最佳组合和其评分
        """
        # 获取前10个可能的候选组合
        candidates = []
        for (group, score) in sorted(results, key=lambda x: x[1], reverse=True):
            if not any(m in used_missiles for m in group):
                candidates.append((group, score))
                if len(candidates) >= 10:  # 只考虑前10个高分组合
                    break
        
        if not candidates:
            return None, 0
        
        # 局部视野搜索
        best_first_group = None
        best_total_score = -float('inf')
        
        for first_group, first_score in candidates:
            # 模拟选择该组合
            temp_used = used_missiles.union(set(first_group))
            total_score = first_score
            
            # 添加启发式评估值
            potential = self._estimate_future_potential(first_group, temp_used)
            total_score += potential
            
            # 如果look_ahead>0，模拟未来look_ahead步的选择
            if look_ahead > 0:
                # 递归计算未来look_ahead步的最佳选择
                next_score = self._simulate_future_selections(
                    temp_used, results, missile_index, look_ahead)
                total_score += next_score
            
            # 更新最佳选择
            if total_score > best_total_score:
                best_total_score = total_score
                best_first_group = first_group
        
        # 返回最佳的第一步选择
        if best_first_group:
            # 找到组合的实际评分
            actual_score = 0
            for (a, b, c), score in results:
                if (a, b, c) == best_first_group:
                    actual_score = score
                    break
            return best_first_group, actual_score
        
        return None, 0
    
    def _simulate_future_selections(self, used_missiles, results, missile_index, depth):
        """模拟未来几步的选择，估计总得分"""
        if depth <= 0 or len(used_missiles) >= self.total_missiles:
            return 0
        
        # 获取可能的下一步选择
        candidates = []
        for (group, score) in sorted(results, key=lambda x: x[1], reverse=True):
            if not any(m in used_missiles for m in group):
                candidates.append((group, score))
                if len(candidates) >= 5:  # 限制搜索宽度
                    break
        
        if not candidates:
            return 0
        
        # 取最高分组合进行模拟
        best_next_group, best_next_score = candidates[0]
        temp_used = used_missiles.union(set(best_next_group))
        
        # 递归计算后续步骤
        future_score = best_next_score + self._simulate_future_selections(
            temp_used, results, missile_index, depth-1)
        
        return future_score
    
    def _estimate_future_potential(self, group, used_missiles):
        """估计选择某组合后对未来可能得分的影响"""
        # 提取最后一枚导弹作为连接点
        last_missile = group[-1]
        row, col = self.get_row(last_missile), self.get_col(last_missile)
        
        # 计算未使用导弹的位置分布
        unused_missiles = [m for m in range(1, self.total_missiles + 1) 
                         if m not in used_missiles and m not in group]
        
        if not unused_missiles:  # 如果没有未使用的导弹了
            return 0
        
        unused_positions = [(self.get_row(m), self.get_col(m)) for m in unused_missiles]
        
        # 评估未来连接的可能性
        # 1. 检查有多少导弹在不同列（一左一右原则）
        different_col = sum(1 for r, c in unused_positions if c != col)
        
        # 2. 检查有多少导弹在较远行（一前一后原则）
        distant_rows = sum(1 for r, c in unused_positions 
                          if abs(r - row) >= max(1, self.rows//4))
        
        # 综合评分
        potential = 0.0
        if unused_missiles:  # 防止除零错误
            potential = 0.6 * (different_col/len(unused_missiles)) + \
                        0.4 * (distant_rows/len(unused_missiles))
        
        # 缩放系数，影响不要过大
        return potential * 0.1
    
    def _get_best_pair(self, used_missiles, results, missile_index):
        """查找最佳的两枚导弹组合"""
        n = self.total_missiles
        available_missiles = [m for m in range(1, n+1) if m not in used_missiles]
        
        if len(available_missiles) < 2:
            return None, 0
        
        best_pair = None
        best_pair_score = -1
        
        # 遍历所有可能的2发组合
        for i in range(len(available_missiles)):
            for j in range(i+1, len(available_missiles)):
                a, b = available_missiles[i], available_missiles[j]
                
                # 计算这两枚导弹的得分，基于它们在三发组合中的表现
                # 搜集两枚导弹一起出现的组合
                pair_scores = []
                for (x, y, z), score in results:
                    if (a in (x, y, z)) and (b in (x, y, z)):
                        pair_scores.append(score)
                
                if pair_scores:
                    # 使用它们在三发组合中的平均得分
                    pair_score = sum(pair_scores) / len(pair_scores)
                else:
                    # 如果没有一起出现过，直接使用我们的评分函数
                    pair_score = self.score(a, b)
                    
                    # 添加位置多样性考虑
                    row_a, col_a = self.get_row(a), self.get_col(a)
                    row_b, col_b = self.get_row(b), self.get_col(b)
                    
                    # 稍微降低二发组评分，保持与三发组的区别
                    pair_score = pair_score * 0.95
                    
                if pair_score > best_pair_score:
                    best_pair_score = pair_score
                    best_pair = (a, b)
        
        return best_pair, best_pair_score
    
    def analyze_top_scores(self, results, top_n=10):
        """分析得分最高的组合，输出详细评分过程"""
        top_results = sorted(results, key=lambda x: x[1], reverse=True)[:top_n]
        
        analysis = []
        for i, ((a, b, c), score) in enumerate(top_results):
            detail = {
                "排名": i+1,
                "组合": f"{a}-{b}-{c}",
                "总分": f"{score:.4f}",
                "A→B距离分": f"{self.distance_score(a, b):.4f}",
                "A→B交替分": f"{self.side_alternation_score(a, b):.4f}",
                "B→C距离分": f"{self.distance_score(b, c):.4f}",
                "B→C交替分": f"{self.side_alternation_score(b, c):.4f}",
                "位置多样性": f"{self.position_diversity(a, b, c):.4f}",
                "A位置": f"({self.get_row(a)},{self.get_col(a)})",
                "B位置": f"({self.get_row(b)},{self.get_col(b)})",
                "C位置": f"({self.get_row(c)},{self.get_col(c)})"
            }
            analysis.append(detail)
        
        return analysis

def main():
    """主函数，处理用户输入并运行评估"""
    valid_counts = [4, 8, 12, 16, 32]
    
    while True:
        try:
            n = int(input(f"请输入导弹数量(可选{valid_counts}): "))
            if n in valid_counts:
                break
            else:
                print(f"请输入有效的导弹数量: {valid_counts}")
        except ValueError:
            print("请输入有效的数字")
    
    # 创建评估器
    evaluator = MissileLaunchEvaluator(num_missiles=n)
    
    # 询问是否要指定首发导弹
    first_missile = None
    while True:
        specify_first = input("是否要指定首发DD？(y/n): ").strip().lower()
        if specify_first == 'y':
            try:
                first_missile_input = input(f"请输入首发DD编号(1-{n})，或输入0取消: ")
                first_missile = int(first_missile_input)
                if first_missile == 0:
                    first_missile = None
                    print("已取消首发导弹指定")
                    break
                elif 1 <= first_missile <= n:
                    print(f"已指定DD{first_missile}为首发DD")
                    break
                else:
                    print(f"请输入有效的导弹编号(1-{n})或0取消")
            except ValueError:
                print("请输入有效的数字")
        elif specify_first == 'n':
            break
        else:
            print("请输入 y 或 n")
    
    # 计算所有可能组合
    results = evaluator.compute_all_combinations()
    
    # 保存结果到Excel
    output_file = f"missile_launch_scores_n{n}.xlsx"
    evaluator.save_to_excel(results, output_file)
    
    # 显示Top 5最高分组合
    print("\n评分最高的前5个发射组合:")
    top_5 = sorted(results, key=lambda x: x[1], reverse=True)[:5]
    for i, ((a, b, c), score) in enumerate(top_5):
        print(f"组合 {i+1}: {a}-{b}-{c}, 得分: {score:.4f}")
    
    # 寻找最优发射序列
    print("\n计算最优发射序列...")
    optimal_sequence, raw_total_score = evaluator.find_optimal_launch_sequence(results, first_missile)
    
    # 文件名添加首发导弹信息
    first_missile_suffix = f"_first{first_missile}" if first_missile else ""
    
    # 重新从序列计算各组得分，而不是使用算法返回的总分
    group_scores = []
    for i in range(0, n, 3):
        if i+2 < n:
            # 完整的三发组合
            group = tuple(optimal_sequence[i:i+3])
            group_score = 0
            for (a, b, c), score in results:
                if group == (a, b, c):
                    group_score = score
                    break
            group_scores.append(group_score)
        else:
            # 处理最后不足3发的情况
            group = optimal_sequence[i:]
            
            # 计算不足3发组合的得分
            group_score = 0
            
            # 单枚导弹的情况
            if len(group) == 1:
                last_missile = group[0]
                relevant_scores = []
                for (a, b, c), score in results:
                    if last_missile in (a, b, c):
                        relevant_scores.append(score)
                
                if relevant_scores:
                    group_score = sum(relevant_scores) / len(relevant_scores)
            
            # 两枚导弹的情况
            elif len(group) == 2:
                a, b = group
                pair_scores = []
                for (x, y, z), score in results:
                    if (a in (x, y, z)) and (b in (x, y, z)):
                        pair_scores.append(score)
                
                if pair_scores:
                    group_score = sum(pair_scores) / len(pair_scores)
                else:
                    # 如果没有一起出现过，直接使用评分函数
                    group_score = evaluator.score(a, b) * 0.95  # 与_get_best_pair保持一致
            
            group_scores.append(group_score)
    
    # 正确计算平均得分
    true_avg_score = sum(group_scores) / len(group_scores)
    
    # 打印最优序列
    print(f"\n最优发射序列 ({n}发DD):")
    sequence_str = "-".join(str(m) for m in optimal_sequence)
    print(sequence_str)
    print(f"总得分: {true_avg_score:.4f}")  # 显示正确计算的平均得分
    print(f"平均单组得分: {true_avg_score:.4f}")
    
    # 保存详细分析到Excel
    analysis = evaluator.analyze_top_scores(results, top_n=20)
    analysis_df = pd.DataFrame(analysis)
    analysis_file = f"missile_launch_analysis_n{n}{first_missile_suffix}.xlsx"
    analysis_df.to_excel(analysis_file, index=False)
    print(f"\n详细分析已保存到 {analysis_file}")
    
    # 保存最优序列到文件
    sequence_file = f"optimal_launch_sequence_n{n}{first_missile_suffix}.txt"
    with open(sequence_file, "w") as f:
        if first_missile:
            f.write(f"最优发射序列 ({n}发DD, 首发DD: {first_missile}):\n")
        else:
            f.write(f"最优发射序列 ({n}发DD):\n")
        f.write(f"{sequence_str}\n")
        f.write(f"总得分: {true_avg_score:.4f}\n")
        f.write(f"平均单组得分: {true_avg_score:.4f}\n")
        
        # 单独添加首发导弹信息
        if first_missile:
            f.write(f"首发DD: {first_missile}\n")
        else:
            f.write("首发DD: 未指定\n")
        
        # 打印详细的三发一组的分解
        f.write("\n按三发一组的详细分解:\n")
        for i in range(len(group_scores)):
            if i * 3 + 2 < n:  # 完整的三发组
                group = optimal_sequence[i*3:i*3+3]
                f.write(f"组 {i+1}: {group[0]}-{group[1]}-{group[2]}, 得分: {group_scores[i]:.4f}\n")
            else:  # 不足三发的组
                group = optimal_sequence[i*3:]
                f.write(f"组 {i+1}: {'-'.join(str(m) for m in group)}, 得分: {group_scores[i]:.4f}\n")
    
    print(f"最优发射序列已保存到 {sequence_file}")

if __name__ == "__main__":
    main() 