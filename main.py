import os
import sys
import argparse
import pandas as pd
from tqdm import tqdm
import multiprocessing as mp
import copy

# 导入评估器和W=5生成器
from missile_launch_evaluator import MissileLaunchEvaluator
from missile_launch_w5_generator import MissileLaunchW5Generator, generate_w5_dataset_from_w3

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='导弹发射顺序评估工具')
    parser.add_argument('-n', '--num_missiles', type=int, default=None, 
                        choices=[4, 8, 12, 16, 32],
                        help='导弹数量，可选值为4、8、12、16或32')
    parser.add_argument('-f', '--first_missile', type=int, default=None,
                        help='指定首发导弹编号，如不指定则自动选择')
    parser.add_argument('--top_percent', type=float, default=None,
                        help='选择前多少比例的W=3组合（0-1之间）')
    parser.add_argument('--analyze_only', action='store_true',
                        help='仅分析已有结果，不计算最优序列')
    parser.add_argument('--top_n', type=int, default=10,
                        help='显示前多少个高分组合')
    parser.add_argument('--interactive', action='store_true',
                        help='交互式输入W=5参数模式')
    return parser.parse_args()

def get_user_input_parameters(top_percent=None):
    """获取用户输入的W=5参数"""
    print("\n请输入W=5生成算法参数（直接回车使用默认值）：")
    
    # 获取top_percent
    if top_percent is None:
        top_percent_str = input(f"选择前多少比例的W=3组合 (0-1之间，默认0.2): ").strip()
        top_percent = 0.2
        if top_percent_str:
            try:
                top_percent = float(top_percent_str)
                if top_percent <= 0 or top_percent > 1:
                    print("比例必须在0-1之间，使用默认值0.2")
                    top_percent = 0.2
            except ValueError:
                print("输入无效，使用默认值0.2")
    
    # 获取alpha参数
    alpha1_str = input(f"第一个W=3子序列权重 (默认0.35): ").strip()
    alpha1 = 0.35
    if alpha1_str:
        try:
            alpha1 = float(alpha1_str)
        except ValueError:
            print("输入无效，使用默认值0.35")
            
    alpha2_str = input(f"中间W=3子序列权重 (默认0.30): ").strip()
    alpha2 = 0.30
    if alpha2_str:
        try:
            alpha2 = float(alpha2_str)
        except ValueError:
            print("输入无效，使用默认值0.30")
    
    alpha3_str = input(f"最后一个W=3子序列权重 (默认0.35): ").strip()
    alpha3 = 0.35
    if alpha3_str:
        try:
            alpha3 = float(alpha3_str)
        except ValueError:
            print("输入无效，使用默认值0.35")
    
    # 检查权重总和是否为1.0
    total_weight = alpha1 + alpha2 + alpha3
    if abs(total_weight - 1.0) > 0.001:
        print(f"警告：权重总和为{total_weight}，不为1.0，将进行归一化处理")
        factor = 1.0 / total_weight
        alpha1 *= factor
        alpha2 *= factor
        alpha3 *= factor
        print(f"归一化后的权重：{alpha1:.3f}, {alpha2:.3f}, {alpha3:.3f}")
    
    # 确认进程数
    num_processes_str = input(f"并行处理的进程数 (默认使用CPU核心数{mp.cpu_count()}): ").strip()
    num_processes = mp.cpu_count()
    if num_processes_str:
        try:
            num_processes = int(num_processes_str)
            if num_processes < 1:
                print(f"进程数必须大于0，使用默认值{mp.cpu_count()}")
                num_processes = mp.cpu_count()
        except ValueError:
            print(f"输入无效，使用默认值{mp.cpu_count()}")
    
    print(f"\n使用参数：top_percent={top_percent}, alpha1={alpha1:.3f}, alpha2={alpha2:.3f}, alpha3={alpha3:.3f}, num_processes={num_processes}")
    
    return {
        'top_percent': top_percent,
        'alpha1': alpha1,
        'alpha2': alpha2,
        'alpha3': alpha3,
        'num_processes': num_processes
    }

def load_w3_results(evaluator, n):
    """加载或计算W=3评分结果"""
    w3_file = f"missile_launch_w3_scores_n{n}.xlsx"
    
    if os.path.exists(w3_file):
        print(f"从文件加载W=3数据集: {w3_file}")
        df = pd.read_excel(w3_file)
        w3_results = []
        for _, row in df.iterrows():
            sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
            score = row['评分']
            w3_results.append((sequence, score))
    else:
        print("计算W=3数据集...")
        w3_results = evaluator.compute_all_combinations()
        evaluator.save_to_excel(w3_results, w3_file)
    
    return w3_results

def load_w5_results(evaluator, w3_results, num_missiles, params=None):
    """加载或计算W=5评分结果
    
    Args:
        evaluator: 评估器实例
        w3_results: W=3评分结果列表
        num_missiles: 导弹数量
        params: W=5生成参数字典
        
    Returns:
        W=5评分结果列表
    """
    # 设置默认参数
    if params is None:
        params = {
            'top_percent': 0.2,
            'alpha1': 0.35,
            'alpha2': 0.30,
            'alpha3': 0.35,
            'num_processes': mp.cpu_count()
        }
    
    # 准备文件路径
    w5_file = f"missile_launch_w5_scores_n{num_missiles}.xlsx"
    w5_csv_file = f"missile_launch_w5_scores_n{num_missiles}.csv"
    
    # 检查是否存在已计算的结果文件
    w5_results = []
    # 首先尝试从Excel文件加载
    if os.path.exists(w5_file):
        print(f"从文件加载W=5数据集: {w5_file}")
        df = pd.read_excel(w5_file)
        for _, row in df.iterrows():
            sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
            score = row['评分']
            w5_results.append((sequence, score))
    # 如果Excel文件不存在，尝试从CSV文件加载
    elif os.path.exists(w5_csv_file):
        print(f"从CSV文件加载W=5数据集: {w5_csv_file}")
        df = pd.read_csv(w5_csv_file)
        for _, row in df.iterrows():
            sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
            score = row['评分']
            w5_results.append((sequence, score))
    
    # 如果没有找到已计算的结果
    if not w5_results:
        print("生成W=5数据集...")
        print(f"使用参数：top_percent={params['top_percent']}, alpha1={params['alpha1']:.3f}, alpha2={params['alpha2']:.3f}, alpha3={params['alpha3']:.3f}, num_processes={params['num_processes']}")
        
        # 生成W=5数据集
        w5_results = generate_w5_dataset_from_w3(
            w3_results, evaluator, params['num_processes'],
            params['top_percent'], params['alpha1'], params['alpha2'], params['alpha3'])
        
        # 保存结果到Excel
        generator = MissileLaunchW5Generator(
            evaluator, params['num_processes'], params['top_percent'], 
            params['alpha1'], params['alpha2'], params['alpha3'])
        generator.save_to_excel(w5_results, w5_file)
    
    return w5_results

def find_optimal_sequence(evaluator, results, n, first_missile=None, window_size=3):
    """查找最优发射序列
    
    注意：这个函数不再直接调用evaluator.find_optimal_launch_sequence，
    而是根据窗口大小使用不同的算法
    """
    print(f"\n计算最优发射序列 (W={window_size})...")
    
    # 文件名添加首发导弹信息和窗口大小
    first_missile_suffix = f"_first{first_missile}" if first_missile else ""
    window_suffix = f"_w{window_size}"
    
    # 根据窗口大小选择不同的算法
    if window_size == 3:
        # W=3使用原始评估器的方法
        optimal_sequence, raw_total_score = evaluator.find_optimal_launch_sequence(results, first_missile)
    else:
        # W=5使用自定义贪心算法
        optimal_sequence, raw_total_score = find_optimal_w5_sequence(evaluator, results, n, first_missile)
    
    # 重新从序列计算各组得分
    group_scores = []
    
    if window_size == 3:
        # 对于W=3，按照原始评估器的方法计算
        for i in range(0, n, 3):
            if i+2 < n:
                # 完整的三发组合
                group = tuple(optimal_sequence[i:i+3])
                group_score = 0
                for (a, b, c), score in results:
                    if group == (a, b, c):
                        group_score = score
                        break
                group_scores.append(group_score)
            else:
                # 处理最后不足3发的情况
                group = optimal_sequence[i:]
                
                # 计算不足3发组合的得分
                group_score = 0
                
                # 单枚导弹的情况
                if len(group) == 1:
                    last_missile = group[0]
                    relevant_scores = []
                    for (a, b, c), score in results:
                        if last_missile in (a, b, c):
                            relevant_scores.append(score)
                    
                    if relevant_scores:
                        group_score = sum(relevant_scores) / len(relevant_scores)
                
                # 两枚导弹的情况
                elif len(group) == 2:
                    a, b = group
                    pair_scores = []
                    for (x, y, z), score in results:
                        if (a in (x, y, z)) and (b in (x, y, z)):
                            pair_scores.append(score)
                    
                    if pair_scores:
                        group_score = sum(pair_scores) / len(pair_scores)
                    else:
                        # 如果没有一起出现过，直接使用评分函数
                        group_score = evaluator.score(a, b) * 0.95  # 与_get_best_pair保持一致
                
                group_scores.append(group_score)
    else:
        # 对于W=5，按照W=5的方式计算
        for i in range(0, n, 5):
            if i+4 < n:
                # 完整的五发组合
                group = tuple(optimal_sequence[i:i+5])
                group_score = 0
                for sequence, score in results:
                    if group == sequence:
                        group_score = score
                        break
                # 确保得分不为0，至少应该是0.9（评分系统的最低分）
                if group_score == 0:
                    # 如果找不到匹配的组合，使用这些导弹参与的W=5组合的平均分
                    relevant_missiles = set(group)
                    relevant_scores = []
                    
                    for sequence, score in results:
                        if any(m in relevant_missiles for m in sequence):
                            relevant_scores.append(score)
                    
                    if relevant_scores:
                        group_score = sum(relevant_scores) / len(relevant_scores)
                    else:
                        # 如果仍然找不到相关分数，设置为默认最低分0.9
                        group_score = 0.9
                
                group_scores.append(group_score)
            else:
                # 处理最后不足5发的情况
                group = optimal_sequence[i:]
                
                # 使用这些导弹参与的W=5组合的平均分
                relevant_missiles = set(group)
                relevant_scores = []
                
                for sequence, score in results:
                    if any(m in relevant_missiles for m in sequence):
                        relevant_scores.append(score)
                
                if relevant_scores:
                    group_score = sum(relevant_scores) / len(relevant_scores)
                    group_scores.append(group_score)
                else:
                    # 如果找不到相关分数，设置为默认最低分0.9
                    group_scores.append(0.9)
    
    # 正确计算平均得分
    true_avg_score = sum(group_scores) / len(group_scores) if group_scores else 0
    
    # 打印最优序列
    print(f"\n最优发射序列 ({n}发DD, W={window_size}):")
    sequence_str = "-".join(str(m) for m in optimal_sequence)
    print(sequence_str)
    print(f"总得分: {true_avg_score:.4f}")
    print(f"平均单组得分: {true_avg_score:.4f}")
    
    # 保存最优序列到文件
    sequence_file = f"optimal_launch_sequence_n{n}{window_suffix}{first_missile_suffix}.txt"
    with open(sequence_file, "w") as f:
        if first_missile:
            f.write(f"最优发射序列 ({n}发DD, W={window_size}, 首发DD: {first_missile}):\n")
        else:
            f.write(f"最优发射序列 ({n}发DD, W={window_size}):\n")
        f.write(f"{sequence_str}\n")
        f.write(f"总得分: {true_avg_score:.4f}\n")
        f.write(f"平均单组得分: {true_avg_score:.4f}\n")
        
        # 单独添加首发导弹信息
        if first_missile:
            f.write(f"首发DD: {first_missile}\n")
        else:
            f.write("首发DD: 未指定\n")
        
        # 打印详细的分组分解
        group_size = window_size
        f.write(f"\n按{group_size}发一组的详细分解:\n")
        for i in range(len(group_scores)):
            start_idx = i * group_size
            end_idx = min(start_idx + group_size, n)
            group = optimal_sequence[start_idx:end_idx]
            f.write(f"组 {i+1}: {'-'.join(str(m) for m in group)}, 得分: {group_scores[i]:.4f}\n")
    
    print(f"最优发射序列已保存到 {sequence_file}")
    
    return optimal_sequence, true_avg_score

def find_optimal_w5_sequence(evaluator, results, n, first_missile=None):
    """为W=5数据集查找最优发射序列的自定义算法
    
    Args:
        evaluator: W=3评估器实例
        results: W=5评分结果列表，格式为[(tuple(a,b,c,d,e), score), ...]
        n: 导弹总数
        first_missile: 指定的首发导弹编号，如果为None则不限制
        
    Returns:
        最优发射序列和总得分
    """
    # 构建导弹ID到W=5组合的查找表
    missile_index = {}
    for i in range(1, n+1):
        missile_index[i] = []
    
    # 遍历所有W=5组合
    for sequence, score in results:
        # 将组合添加到包含的每个导弹的列表中
        for missile in sequence:
            missile_index[missile].append((sequence, score))
    
    # 对每个导弹的组合按分数降序排序
    for i in range(1, n+1):
        missile_index[i].sort(key=lambda x: x[1], reverse=True)
    
    # 创建导弹使用状态记录
    used_missiles = set()
    optimal_sequence = []
    total_score = 0
    
    # 如果指定了首发导弹，先将其添加到序列中
    if first_missile is not None:
        if first_missile < 1 or first_missile > n:
            print(f"警告: 指定的首发导弹 {first_missile} 无效，将忽略此限制")
        else:
            optimal_sequence.append(first_missile)
            used_missiles.add(first_missile)
            print(f"已指定DD {first_missile} 为首发DD")
    
    # 贪心算法主循环
    while len(used_missiles) < n:
        # 找出当前最佳组合
        best_group = None
        best_score = -1
        
        # 如果序列为空，选择评分最高的组合
        if len(optimal_sequence) == 0:
            for group, score in sorted(results, key=lambda x: x[1], reverse=True):
                if not any(m in used_missiles for m in group):
                    best_group = group
                    best_score = score
                    break
            
            if best_group:
                # 添加整个组合
                for missile in best_group:
                    optimal_sequence.append(missile)
                    used_missiles.add(missile)
                total_score += best_score
                continue
        
        # 如果序列不为空，查找包含最后一枚导弹的最佳组合
        if len(optimal_sequence) > 0:
            last_missile = optimal_sequence[-1]
            for group, score in missile_index[last_missile]:
                # 检查组合中的导弹是否已使用
                unused_missiles = [m for m in group if m not in used_missiles]
                
                # 如果组合中有未使用的导弹，并且包含最后一枚导弹
                if unused_missiles and last_missile in group:
                    # 计算组合中未使用导弹的位置
                    group_list = list(group)
                    last_idx = group_list.index(last_missile)
                    
                    # 只考虑最后一枚导弹后面的导弹
                    next_missiles = [m for m in group_list[last_idx+1:] if m not in used_missiles]
                    
                    # 如果有可用的后续导弹
                    if next_missiles:
                        # 计算组合质量
                        quality = score * (len(next_missiles) / len(group))
                        
                        if quality > best_score:
                            best_score = quality
                            best_group = next_missiles
        
            # 如果找到了最佳组合，添加到序列中
            if best_group:
                for missile in best_group:
                    optimal_sequence.append(missile)
                    used_missiles.add(missile)
                # 按比例添加得分
                total_score += best_score
                continue
        
        # 如果没有找到合适的组合，选择任意未使用的导弹
        remaining = [m for m in range(1, n+1) if m not in used_missiles]
        if remaining:
            # 查找评分最高的单枚导弹
            best_single = None
            best_single_score = -1
            
            for missile in remaining:
                # 计算该导弹参与的所有组合的平均分
                scores = [score for group, score in missile_index[missile]]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    if avg_score > best_single_score:
                        best_single_score = avg_score
                        best_single = missile
            
            if best_single:
                optimal_sequence.append(best_single)
                used_missiles.add(best_single)
                total_score += best_single_score
                continue
        
        # 如果无法找到任何可行的导弹，退出循环
        break
    
    return optimal_sequence, total_score

def analyze_results(results, top_n=10, window_size=3):
    """分析评分结果"""
    print(f"\nW={window_size}评分最高的前{top_n}个组合:")
    
    # 按评分排序
    top_results = sorted(results, key=lambda x: x[1], reverse=True)[:top_n]
    
    for i, (sequence, score) in enumerate(top_results):
        sequence_str = "-".join(str(m) for m in sequence)
        print(f"{i+1}. {sequence_str}, 评分: {score:.4f}")

def main():
    """主函数 - 修改为同时计算W=3和W=5的结果，并允许用户输入关键参数"""
    # 解析命令行参数
    args = parse_args()
    
    # 获取用户输入的导弹数量
    num_missiles = args.num_missiles
    if num_missiles is None:
        valid_counts = [4, 8, 12, 16, 32]
        while True:
            try:
                num_input = input(f"请输入导弹数量(可选{valid_counts}，默认16): ")
                if not num_input:
                    num_missiles = 16  # 默认值
                    break
                num_missiles = int(num_input)
                if num_missiles in valid_counts:
                    break
                else:
                    print(f"请输入有效的导弹数量: {valid_counts}")
            except ValueError:
                print("请输入有效的数字")
    
    # 获取用户输入的首发导弹
    first_missile = args.first_missile
    if first_missile is None:
        while True:
            first_input = input(f"请输入首发导弹编号(1-{num_missiles}，直接回车表示不指定): ")
            if not first_input:
                first_missile = None  # 不指定
                break
            try:
                first_missile = int(first_input)
                if 1 <= first_missile <= num_missiles:
                    break
                else:
                    print(f"请输入有效的导弹编号(1-{num_missiles})或直接回车")
            except ValueError:
                print("请输入有效的数字或直接回车")
    
    # 获取用户输入的top_percent
    top_percent = args.top_percent
    if top_percent is None:
        while True:
            top_percent_input = input("请输入选择前多少比例的W=3组合(0-1之间，默认0.2): ")
            if not top_percent_input:
                top_percent = 0.2  # 默认值
                break
            try:
                top_percent = float(top_percent_input)
                if 0 < top_percent <= 1:
                    break
                else:
                    print("请输入0-1之间的数值")
            except ValueError:
                print("请输入有效的数字")
    
    # 打印分隔线
    print("\n" + "="*80)
    print(f"开始导弹发射顺序优化 (导弹数量: {num_missiles})")
    if first_missile:
        print(f"首发导弹: {first_missile}")
    print(f"W=5参数 - top_percent: {top_percent}")
    print("="*80)
    
    # 创建评估器
    evaluator = MissileLaunchEvaluator(num_missiles=num_missiles)
    
    # ========== W=3评估部分 ==========
    print("\n" + "="*80)
    print("【W=3评估】基于三枚导弹窗口")
    print("="*80)
    
    # 加载或计算W=3结果
    w3_results = load_w3_results(evaluator, num_missiles)
    
    # 分析W=3结果
    analyze_results(w3_results, args.top_n, window_size=3)
    
    # 如果不是仅分析模式，计算W=3的最优序列
    if not args.analyze_only:
        w3_sequence, w3_score = find_optimal_sequence(evaluator, w3_results, num_missiles, 
                            first_missile, window_size=3)
    
    # ========== W=5评估部分 ==========
    print("\n" + "="*80)
    print("【W=5评估】基于五枚导弹窗口")
    print("="*80)
    
    # 如果是交互模式，获取用户输入的参数
    w5_params = None
    if args.interactive:
        w5_params = get_user_input_parameters(top_percent)
    else:
        # 使用命令行参数或默认值
        w5_params = {
            'top_percent': top_percent,
            'alpha1': 0.35,
            'alpha2': 0.30,
            'alpha3': 0.35,
            'num_processes': mp.cpu_count()
        }
        
    # 加载或计算W=5结果
    w5_results = load_w5_results(evaluator, w3_results, num_missiles, w5_params)
    
    # 分析W=5结果
    analyze_results(w5_results, args.top_n, window_size=5)
    
    # 如果不是仅分析模式，计算W=5的最优序列
    if not args.analyze_only:
        w5_sequence, w5_score = find_optimal_sequence(evaluator, w5_results, num_missiles, 
                            first_missile, window_size=5)
    
    # ========== 结果比较 ==========
    if not args.analyze_only:
        print("\n" + "="*80)
        print("【结果比较】W=3 vs W=5")
        print("="*80)
        print(f"\nW=3平均得分: {w3_score:.4f}")
        print(f"W=5平均得分: {w5_score:.4f}")
        print(f"得分差异: {w5_score - w3_score:.4f}")
        
        # 比较两种序列的差异
        w3_set = set(zip(w3_sequence[:-1], w3_sequence[1:]))
        w5_set = set(zip(w5_sequence[:-1], w5_sequence[1:]))
        common_pairs = w3_set.intersection(w5_set)
        
        similarity = len(common_pairs) / len(w3_set) if w3_set else 0
        print(f"序列相似度: {similarity:.2f} (共有{len(common_pairs)}个相同的相邻对)")
    
    print("\n" + "="*80)
    print("导弹发射顺序优化完成")
    print("="*80)

if __name__ == "__main__":
    main() 