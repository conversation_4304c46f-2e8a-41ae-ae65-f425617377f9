"""
引入动态特征，计算欧几里得距离影响，同时再引入概率性不合格以及一个强约束
增加标签：下一个最优的导弹编号
增加输出每一组数据的发射顺序功能
增加输出无法发射的导弹编号
"""
import math
import random

import pandas as pd

# 参数设置
N = 16                           # 每组DD数量
GROUP_SIZE = 4                   # 每组对应的DD数量
PROB_STRONG_CONSTRAINT = 0.05    # 强约束概率
PROB_MASS_LOW = 0.005            # 质量分不合格概率
PROB_PIPELINE_UNHEALTHY = 0.003  # 管路不健康概率
PROB_CIRCUIT_UNHEALTHY = 0.005   # 电路不健康概率
ROWS, COLS = 8, 2                # 布局为 8行×2列
K = 10                           # 动态影响系数
NUM_GROUPS = 100                 # 总数据组数

# 初始化导弹位置
def initialize_positions(num_missiles, rows, cols):
    positions = {}
    for i in range(1, num_missiles + 1):
        row = (i - 1) // cols + 1
        col = (i - 1) % cols + 1
        positions[i] = (row, col)
    return positions

# 计算欧几里得距离
def euclidean_distance(pos1, pos2):
    return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

# 动态特征更新函数
def update_dynamic_features(missiles, positions, fired_missile_id):
    fired_pos = positions[fired_missile_id]
    for missile in missiles:
        if missile["id"] != fired_missile_id and missile["valid"] == 1:
            distance = euclidean_distance(fired_pos, positions[missile["id"]])
            missile["dynamic_impact"] += K / (distance + 1e-6)  # 避免除以零

# 生成DD数据
def generate_missile_data():
    missiles = []
    pipeline_health = []  # 每组管路的健康分

    # 为每组生成管路健康分
    for i in range(N // GROUP_SIZE):
        health = random.randint(80, 100)
        if random.random() < PROB_PIPELINE_UNHEALTHY:  # 0.3% 概率管路不健康
            health = random.randint(50, 79)
        pipeline_health.append(health)

    for i in range(1, N + 1):
        # 强约束
        strong_constraint = 1 if random.random() < PROB_STRONG_CONSTRAINT else 0

        # 质量分
        mass = random.randint(90, 100)
        if random.random() < PROB_MASS_LOW:  # 0.5% 概率质量分不合格
            mass = random.randint(70, 89)

        # 管路健康分（每 4 个DD共享一个管路）
        pipeline_idx = (i - 1) // GROUP_SIZE
        pipeline_health_score = pipeline_health[pipeline_idx]

        # 电路健康分
        circuit_health = random.randint(80, 100)
        if random.random() < PROB_CIRCUIT_UNHEALTHY:  # 0.5% 概率电路不健康
            circuit_health = random.randint(50, 79)

        # 检查是否有效
        valid = 1  # 默认可以发射
        if (
            strong_constraint == 1
            or mass < 90
            or pipeline_health_score < 80
            or circuit_health < 80
        ):
            valid = 0  # 无法发射

        # 添加到导弹数据
        missiles.append({
            "id": i,
            "strong_constraint": strong_constraint,
            "mass": mass,
            "pipeline_health": pipeline_health_score,
            "circuit_health": circuit_health,
            "dynamic_impact": 0.0,  # 初始动态影响为 0
            "valid": valid  # 是否可以发射
        })

    return missiles

# 生成动态数据集
def generate_dynamic_dataset(num_groups, num_missiles, rows, cols):
    positions = initialize_positions(num_missiles, rows, cols)  # 初始化导弹位置
    dataset = []
    firing_orders = []  # 用于保存每组的发射顺序
    invalid_missiles_per_group = []  # 保存每组无法发射的导弹编号

    for group_id in range(1, num_groups + 1):
        missiles = generate_missile_data()
        fired_missiles = []  # 保存当前组的FS顺序
        invalid_missiles = [m["id"] for m in missiles if m["valid"] == 0]  # 无法FS的DD编号

        while len(fired_missiles) < num_missiles:
            # 排除已FS的DD
            unfired_missiles = [m for m in missiles if m["id"] not in fired_missiles and m["valid"] == 1]

            # 如果没有可FS的DD，则停止
            if not unfired_missiles:
                break

            # 按质量分 + 管路健康分 + 电路健康分 - 动态影响选择最优DDFS
            next_missile = max(
                unfired_missiles,
                key=lambda m: m["mass"] + m["pipeline_health"] + m["circuit_health"] - m["dynamic_impact"]
            )
            next_missile_id = next_missile["id"]  # 记录下一个要FS的DD编号
            fired_missiles.append(next_missile_id)

            # 动态更新其他DD的动态影响特征
            update_dynamic_features(missiles, positions, next_missile_id)

            # 记录当前状态并标注 next_missile_id
            for missile in missiles:
                dataset.append({
                    "group_id": group_id,
                    "id": missile["id"],
                    "strong_constraint": missile["strong_constraint"],
                    "mass": missile["mass"],
                    "pipeline_health": missile["pipeline_health"],
                    "circuit_health": missile["circuit_health"],
                    "dynamic_impact": missile["dynamic_impact"],
                    "valid": missile["valid"],
                    "fired": 1 if missile["id"] in fired_missiles else 0,  # 是否已发射
                    "next_id": next_missile_id  # 标签
                })

        # 保存当前组的发射顺序和无法发射的导弹
        firing_orders.append({
            "group_id": group_id,
            "firing_order": fired_missiles,
            "invalid": invalid_missiles
        })

    return pd.DataFrame(dataset), pd.DataFrame(firing_orders)

# 主函数
def main():
    dataset, firing_orders = generate_dynamic_dataset(NUM_GROUPS, N, ROWS, COLS)

    # 保存数据集
    dataset.to_csv("missile_dynamic_dataset_with_next_missile03.csv", index=False)
    firing_orders.to_csv("missile_firing_orders_and_invalid03.csv", index=False)
    print("动态特征数据集已保存为 missile_dynamic_dataset_with_next_missile03.csv")
    print("发射顺序及无法发射的导弹已保存为 missile_firing_orders_and_invalid03.csv")

    # 显示部分数据
    print("示例数据：")
    print(dataset.head(20))
    print("发射顺序和无法发射导弹示例：")
    print(firing_orders.head(10))

def mydataset():
    dataset, firing_orders = generate_dynamic_dataset(NUM_GROUPS, N, ROWS, COLS)

    # 保存数据集
    dataset.to_csv("dataset.csv", index=False)
    firing_orders.to_csv("orders.csv", index=False)
    print("动态特征数据集已保存为 dataset.csv")
    print("已保存为 orders.csv")

# 执行主函数
if __name__ == "__main__":
    main()






