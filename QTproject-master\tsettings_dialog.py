from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QLineEdit,
    QPushButton, QFormLayout, QMessageBox
)
from PyQt5.QtGui import QDoubleValidator
from PyQt5.QtCore import Qt
import os
import pandas as pd


class TSettingsDialog(QDialog):
    def __init__(self, num_circles, parent=None):
        super().__init__(parent)
        self.num_fields = num_circles // 2 - 1
        self.values = {}
        self.setup_ui()

    def setup_ui(self):
        """初始化对话框界面"""
        self.setWindowTitle("T距设置")
        self.setMinimumSize(300, 400)

        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # 尝试从Tdistance.xlsx读取当前的T距值
        current_values = self.read_tdistance_values()

        # 动态生成r输入框
        for i in range(1, self.num_fields + 1):
            line_edit = QLineEdit()
            line_edit.setValidator(QDoubleValidator())
            
            # 如果从文件中读取到了值，使用文件中的值，否则使用默认值1.0
            if f"r{i}" in current_values:
                line_edit.setText(str(current_values[f"r{i}"]))
            else:
                line_edit.setText("1.0")
                
            form_layout.addRow(QLabel(f"r{i}:"), line_edit)
            self.values[f"r{i}"] = line_edit

        # 添加l输入框
        self.l_edit = QLineEdit()
        self.l_edit.setValidator(QDoubleValidator())
        
        # 如果从文件中读取到了l值，使用文件中的值，否则使用默认值2.0
        if "l" in current_values:
            self.l_edit.setText(str(current_values["l"]))
        else:
            self.l_edit.setText("2.0")
            
        form_layout.addRow(QLabel("l:"), self.l_edit)

        # 确定按钮
        confirm_btn = QPushButton("确定")
        confirm_btn.clicked.connect(self.validate_and_accept)

        layout.addLayout(form_layout)
        layout.addWidget(confirm_btn)
        self.setLayout(layout)

    def read_tdistance_values(self):
        """从Tdistance.xlsx文件读取当前的T距值"""
        try:
            # 获取Tdistance.xlsx文件路径
            tdistance_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Tdistance.xlsx")
            
            # 检查文件是否存在
            if not os.path.exists(tdistance_path):
                return {}  # 如果文件不存在，返回空字典
                
            # 读取Excel文件
            df = pd.read_excel(tdistance_path)
            
            # 如果DataFrame为空，返回空字典
            if df.empty:
                return {}
                
            # 将DataFrame的第一行转换为字典
            values_dict = df.iloc[0].to_dict()
            
            return values_dict
        except Exception as e:
            print(f"读取T距文件失败: {str(e)}")
            return {}  # 如果读取失败，返回空字典

    def validate_and_accept(self):
        """验证输入并接受对话框"""
        try:
            # 验证所有r值
            for key, edit in self.values.items():
                if not edit.text():
                    raise ValueError(f"{key} 不能为空")
                float(edit.text())

            # 验证l值
            if not self.l_edit.text():
                raise ValueError("l 不能为空")
            float(self.l_edit.text())

            self.accept()
        except ValueError as e:
            QMessageBox.warning(self, "输入错误", str(e))

    def get_values(self):
        """获取所有输入值"""
        data = {}
        for key, edit in self.values.items():
            data[key] = float(edit.text())
        data["l"] = float(self.l_edit.text())
        return data