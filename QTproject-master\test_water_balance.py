#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试注水/吹除平衡图表
验证图表功能和动态展示效果
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt
from water_balance_chart import WaterBalanceChart

class WaterBalanceTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("注水/吹除平衡图表测试")
        # 设置窗口尺寸
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d5a87;
                color: white;
                border: 2px solid #00FFFF;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d6a97;
            }
            QPushButton:pressed {
                background-color: #1d4a77;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 左侧控制面板
        self.create_control_panel(main_layout)
        
        # 右侧图表区域
        self.create_chart_area(main_layout)
        
    def create_control_panel(self, main_layout):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_widget.setMaximumWidth(350)
        control_layout = QVBoxLayout()
        control_widget.setLayout(control_layout)
        
        # 标题
        title_label = QLabel("注水/吹除平衡图表测试控制台")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #00FF00; border: 2px solid #00FF00; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel("""
测试说明:
• 平衡系数范围: 0.0 - 1.0
• 绿色区域: 安全 (0.8-1.0)
• 黄色区域: 警告 (0.6-0.8)  
• 红色区域: 危险 (0.0-0.6)
• 动态展示: 按发射顺序更新
• 系统响应: 模拟注水/吹除过程

计算模型:
• 导弹重量影响: 1.5吨/发
• 潜艇排水量: 150吨
• 注水补偿率: 80%
• 系统效率: 95%
• 响应延迟: 300ms
        """)
        info_label.setStyleSheet("background-color: rgba(0, 20, 40, 0.7); border: 1px solid #00FFFF; padding: 10px; font-size: 10px;")
        control_layout.addWidget(info_label)
        
        # 测试按钮
        self.create_test_buttons(control_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 等待测试")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 1px solid #00FF00; padding: 5px; color: #00FF00;")
        control_layout.addWidget(self.status_label)
        
        main_layout.addWidget(control_widget)
        
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        # 测试案例1: 小规模发射
        btn1 = QPushButton("测试1: 4发导弹 (2×2)")
        btn1.clicked.connect(lambda: self.run_test([1, 3, 2, 4], 4, "小规模发射测试"))
        layout.addWidget(btn1)
        
        # 测试案例2: 中等规模发射
        btn2 = QPushButton("测试2: 8发导弹 (2×4)")
        btn2.clicked.connect(lambda: self.run_test([1, 3, 5, 7, 2, 4, 6, 8], 8, "中等规模发射测试"))
        layout.addWidget(btn2)
        
        # 测试案例3: 大规模发射
        btn3 = QPushButton("测试3: 16发导弹 (2×8)")
        btn3.clicked.connect(lambda: self.run_test([1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16, "大规模发射测试"))
        layout.addWidget(btn3)
        
        # 测试案例4: 不均匀发射
        btn4 = QPushButton("测试4: 不均匀序列")
        btn4.clicked.connect(lambda: self.run_test([1, 8, 3, 6, 2, 7, 4, 5], 8, "不均匀发射序列测试"))
        layout.addWidget(btn4)
        
        # 重置按钮
        reset_btn = QPushButton("重置图表")
        reset_btn.clicked.connect(self.reset_chart)
        reset_btn.setStyleSheet("background-color: #8B4513; border: 2px solid #FF6347;")
        layout.addWidget(reset_btn)
        
        # 停止动画按钮
        stop_btn = QPushButton("停止动画")
        stop_btn.clicked.connect(self.stop_animation)
        stop_btn.setStyleSheet("background-color: #B22222; border: 2px solid #FF0000;")
        layout.addWidget(stop_btn)
        
    def create_chart_area(self, main_layout):
        """创建右侧图表区域"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout()
        chart_widget.setLayout(chart_layout)
        
        # 图表标题
        chart_title = QLabel("注水/吹除平衡状态监控")
        chart_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #00FFFF; text-align: center; padding: 10px;")
        chart_title.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_title)
        
        # 创建注水/吹除平衡图表
        self.water_balance_chart = WaterBalanceChart()
        chart_layout.addWidget(self.water_balance_chart)
        
        # 图表说明
        chart_info = QLabel("""
图表说明:
• 蓝绿色曲线: 实时平衡状态
• 填充区域: 平衡变化趋势
• 橙色虚线: 安全下限 (0.8)
• 红色虚线: 危险阈值 (0.6)
• 黄色标注: 当前状态和数值
• 动态更新: 800ms间隔展示
        """)
        chart_info.setStyleSheet("background-color: rgba(20, 20, 20, 0.8); border: 1px solid #00FFFF; padding: 10px; font-size: 11px;")
        chart_layout.addWidget(chart_info)
        
        main_layout.addWidget(chart_widget)
        
    def run_test(self, sequence, missile_count, test_name):
        """运行测试"""
        self.status_label.setText(f"状态: 正在运行 - {test_name}")
        self.status_label.setStyleSheet("background-color: rgba(40, 40, 0, 0.7); border: 1px solid #FFFF00; padding: 5px; color: #FFFF00;")
        
        print(f"\n=== {test_name} ===")
        print(f"发射序列: {sequence}")
        print(f"导弹总数: {missile_count}")
        
        # 更新图表
        self.water_balance_chart.update_chart(sequence, missile_count)
        
        # 更新状态
        self.status_label.setText(f"状态: 动态展示中 - {test_name}")
        
    def reset_chart(self):
        """重置图表"""
        self.water_balance_chart.reset_chart()
        self.status_label.setText("状态: 图表已重置")
        self.status_label.setStyleSheet("background-color: rgba(0, 40, 0, 0.7); border: 1px solid #00FF00; padding: 5px; color: #00FF00;")
        print("图表已重置")
        
    def stop_animation(self):
        """停止动画"""
        self.water_balance_chart.stop_animation()
        self.status_label.setText("状态: 动画已停止")
        self.status_label.setStyleSheet("background-color: rgba(40, 0, 0, 0.7); border: 1px solid #FF0000; padding: 5px; color: #FF0000;")
        print("动画已停止")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = WaterBalanceTestWindow()
    window.show()
    
    print("注水/吹除平衡图表测试程序启动")
    print("请点击左侧按钮进行测试")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
