from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QRadioButton, QButtonGroup, QDialogButtonBox)


class HealthScoreDialog(QDialog):
    def __init__(self, parent=None, option_count=0, selected_dds=None):
        super().__init__(parent)
        self.setWindowTitle('设置DD健康分')
        self.resize(400, 400)
        self.option_count = option_count
        self.selected_dds = selected_dds or []
        self.scores = {}  # {dd_id: score}

        self.setupUI()

    def setupUI(self):
        layout = QVBoxLayout(self)

        if self.option_count <= 0:
            layout.addWidget(QLabel("没有可用的DD", self))
            return

        for i in range(self.option_count):
            dd_id = i + 1
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(f"DD {dd_id}:", self))

            btn_group = QButtonGroup(self)

            # 创建三个分数选项
            for score in [0, 50, 100]:
                radio = QRadioButton(str(score), self)
                radio.setProperty("dd_id", dd_id)
                radio.setProperty("score", score)
                btn_group.addButton(radio, score)
                row_layout.addWidget(radio)

                # 连接信号
                radio.toggled.connect(self.on_score_selected)

            # 如果是已选择的DD，固定为0分并禁用所有选项
            if dd_id in self.selected_dds:
                btn_group.button(0).setChecked(True)
                self.scores[dd_id] = 0
                for radio in btn_group.buttons():
                    radio.setEnabled(False)
            else:
                # 未选择的DD默认选择100分
                btn_group.button(100).setChecked(True)
                self.scores[dd_id] = 100

            layout.addLayout(row_layout)

        # 添加中文按钮
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = btn_box.button(QDialogButtonBox.Ok)
        cancel_button = btn_box.button(QDialogButtonBox.Cancel)
        ok_button.setText("确定")
        cancel_button.setText("取消")

        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def on_score_selected(self, checked):
        radio = self.sender()
        if checked:
            dd_id = radio.property("dd_id")
            score = radio.property("score")
            self.scores[dd_id] = score

    def getScores(self):
        """返回所有设置的分数"""
        return self.scores