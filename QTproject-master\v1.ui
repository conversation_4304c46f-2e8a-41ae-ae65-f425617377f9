<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1800</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>960</width>
    <height>540</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1000000</width>
    <height>1000000</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">* {
    font-family: &quot;Microsoft YaHei&quot;;
}
QWidget#centralwidget{
	border-image: url(:/image/res/background3.png);
}
QPushButton#btnMinimize,#btnClose {
    background-color: transparent;
	border: none;  
}
/* 最小化按钮鼠标悬停状态 */
QPushButton#btnMinimize:hover {
    background-color: #e5e5e5;
}
/* 关闭按钮鼠标悬停状态 */
QPushButton#btnClose:hover {
    background-color: #e81123;
}
/* 最小化按钮按下状态 */
QPushButton#btnMinimize:pressed {
    background-color: #a4a4a4;  
}
/* 关闭按钮按下状态 */
QPushButton#btnClose:pressed {
    background-color: #f1707a;  
}

#pushButton,
#pushButton_2,
#pushButton_3,
#pushButton_4,
#pushButton_7,
#pushButton_8,
#pushButton_9 {
    background-color: #004293;     /* 背景颜色 */
	background-color: rgba(0, 66, 147, 0.5);
    color: #FFFFFF;                /* 文字颜色为白色 */
    text-align: center;            /* 文字居中对齐 */
    padding: 10px 15px;            /* 内边距 */
    border: none;                  /* 无边框 */
 	border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    /*min-height: 40px;*/		   /* 最小高度 */
    font-size: 16px;               /* 字体大小 */
}

#pushButton_10,
#pushButton_11,
#pushButton_5,
#pushButton_6 {
    background-color: #004293;     /* 背景颜色 */
	background-color: rgba(0, 66, 147, 0.5);
    color: #FFFFFF;                /* 文字颜色为白色 */
    text-align: center;            /* 文字居中对齐 */
    padding: 10px 15px;            /* 内边距 */
 	border: 1px solid rgba(255, 255, 255, 0.3);
    /*min-height: 40px;*/		   /* 最小高度 */
    font-size: 16px;               /* 字体大小 */
}

/* 鼠标悬停状态 */
#pushButton:hover:!checked,
#pushButton_2:hover:!checked,
#pushButton_3:hover:!checked,
#pushButton_4:hover:!checked,
#pushButton_7:hover:!checked,
#pushButton_8:hover:!checked,
#pushButton_9:hover:!checked {
    background-color: rgba(255, 255, 255, 0.05);/* 悬停时半透明白色背景 */
	border-left: 3px solid #FFFFFF;
}

#pushButton_5:hover:!checked,
#pushButton_6:hover:!checked,
#pushButton_10:hover:!checked,
#pushButton_11:hover:!checked {
 	background-color: rgba(255, 255, 255, 0.05);
}
/* 按钮按下状态 */
#pushButton:pressed,
#pushButton_2:pressed,
#pushButton_3:pressed,
#pushButton_4:pressed,
#pushButton_7:pressed,
#pushButton_8:pressed,
#pushButton_9:pressed {
    background-color: #037BBF;  /* 蓝色背景 */
    border-left: 3px solid #FFFFFF;  /* 左侧白色竖条 */
}

#pushButton_5:pressed,
#pushButton_6:pressed,
#pushButton_10:pressed,
#pushButton_11:pressed {
 	 background-color: #037BBF;
}
/* 选中状态 */
#pushButton:checked,
#pushButton_2:checked,
#pushButton_3:checked,
#pushButton_4:checked,
#pushButton_5:checked,
#pushButton_6:checked,
#pushButton_7:checked,
#pushButton_8:checked,
#pushButton_9:checked {
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 3px solid #FFFFFF;
}

#pushButton_5:checked,
#pushButton_6:checked,
#pushButton_10:checked,
#pushButton_11:checked {
 	 background-color: rgba(255, 255, 255, 0.1);
}

QStackedWidget {
    border: 1px solid #000000;     /* 基本边框 */
    border-radius: 5px;            /* 圆角 */
    border-style: solid;           /* 边框样式：solid、dashed、dotted */
    border-width: 1px;             /* 边框宽度 */
    border-color: #FFFFFF;         /* 边框颜色 */
}
#label_4,#label_8,#label_9 {
	font-size: 20px;
	font-weight: bold;
    color: #FFFFFF;
}

#label,#label_2,#label_3,#label_5,#label_6,#label_7,#label_10 {
    font-size: 30px;
    font-weight: bold;
    color: #FFFFFF;
	qproperty-alignment: 'AlignHCenter | AlignVCenter';		/* 水平和垂直居中 */
}

/* 表格背景和表头样式 */
QTableWidget {
    background-color: transparent; /* 整体背景透明 */
    color: #FFFFFF; /* 字体颜色 */
    gridline-color: #16213E; /* 网格线颜色 */
    border: 2px solid #0F3460; /* 表格边框颜色 */
}

QHeaderView::section {
    background-color: #0F3460; /* 表头背景颜色 */
    font-weight: bold; /* 表头字体加粗 */
    color: #FFFFFF; /* 表头字体颜色 */
    padding: 4px; /* 表头内边距 */
    border: 1px solid #16213E; /* 表头边框 */
}

/* 左上角白色问题解决 */
QTableCornerButton::section {
    background-color: #0F3460; /* 左上角背景颜色 */
    border: 1px solid #16213E; /* 左上角边框颜色 */
}
#frame_7,#frame_8 {
	border: 1px solid rgba(255, 255, 255, 0.5); 
	border-radius: 5px;
}
#textEdit,#textBrowser,#textBrowser_2 {
	background-color: transparent;
	font-size: 16px; /* 设置字体大小为16px */
    color: #FFFFFF;
}



</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <widget class="QFrame" name="frame_2">
    <property name="geometry">
     <rect>
      <x>160</x>
      <y>10</y>
      <width>122</width>
      <height>22</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
    </layout>
   </widget>
   <widget class="QFrame" name="frame_3">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>10</y>
      <width>150</width>
      <height>22</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
   </widget>
   <widget class="QFrame" name="frame">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>72</y>
      <width>96</width>
      <height>336</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>150</width>
      <height>16777215</height>
     </size>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QPushButton" name="pushButton">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>6</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>物理仿真</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_2">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>53</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>数据加载</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_3">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>100</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>模型训练</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_4">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>147</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>算法演示</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_7">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>194</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>故障注入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_8">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>241</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>参数注入</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_9">
     <property name="geometry">
      <rect>
       <x>1</x>
       <y>288</y>
       <width>94</width>
       <height>42</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>性能评估</string>
     </property>
    </widget>
   </widget>
   <widget class="QStackedWidget" name="stackedWidget">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>90</y>
      <width>1751</width>
      <height>1101</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="accessibleName">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:10pt; color:#fafafa;&quot;&gt;初始化&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
    <property name="styleSheet">
     <string notr="true">border: none; /* 取消边框 */
background-color: black;</string>
    </property>
    <property name="currentIndex">
     <number>5</number>
    </property>
    <widget class="QWidget" name="page">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>物理仿真</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="page_2">
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="1">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>数据集显示区域</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QFrame" name="frame_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>160</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>160</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QPushButton" name="pushButton_6">
           <property name="text">
            <string>生成数据集</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_5">
           <property name="text">
            <string>加载数据集</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QTableWidget" name="tableWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="page_3">
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <item>
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>模型训练</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="page_4">
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="0" column="1" rowspan="2">
       <widget class="QFrame" name="frame_9">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>结果显示区域</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_7">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <property name="bottomMargin">
             <number>40</number>
            </property>
            <item>
             <widget class="QLabel" name="label_8">
              <property name="text">
               <string>最优FS顺序</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTextBrowser" name="textBrowser">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>80</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>80</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_8">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>80</number>
            </property>
            <item>
             <widget class="QLabel" name="label_9">
              <property name="text">
               <string>无法FS的DD</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTextBrowser" name="textBrowser_2">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QFrame" name="frame_6">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <widget class="QPushButton" name="pushButton_11">
         <property name="geometry">
          <rect>
           <x>70</x>
           <y>91</y>
           <width>140</width>
           <height>42</height>
          </rect>
         </property>
         <property name="minimumSize">
          <size>
           <width>140</width>
           <height>42</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>140</width>
           <height>42</height>
          </size>
         </property>
         <property name="text">
          <string>输出结果</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QFrame" name="frame_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <property name="leftMargin">
          <number>50</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="horizontalSpacing">
          <number>20</number>
         </property>
         <property name="verticalSpacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>输入待处理数据的组号</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QTextEdit" name="textEdit">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>33</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>33</height>
            </size>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QPushButton" name="pushButton_10">
           <property name="minimumSize">
            <size>
             <width>140</width>
             <height>42</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>140</width>
             <height>42</height>
            </size>
           </property>
           <property name="text">
            <string>算法处理</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="page_5">
     <layout class="QHBoxLayout" name="horizontalLayout_6">
      <item>
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>故障注入</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
    <widget class="QWidget" name="Setparameter">
     <widget class="QGroupBox" name="AirBox">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>170</y>
        <width>521</width>
        <height>151</height>
       </rect>
      </property>
      <property name="cursor">
       <cursorShape>ArrowCursor</cursorShape>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
	 background-image: url('res/ddjkf(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QPushButton" name="Qx">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>60</y>
         <width>131</width>
         <height>61</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QPushButton" name="Health">
       <property name="geometry">
        <rect>
         <x>290</x>
         <y>60</y>
         <width>131</width>
         <height>61</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="Eletric">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>320</y>
        <width>501</width>
        <height>151</height>
       </rect>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
    background-image: url('res/Zz(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QLineEdit" name="Fs_sum">
       <property name="geometry">
        <rect>
         <x>90</x>
         <y>73</y>
         <width>28</width>
         <height>21</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true"> border: 0.5px solid white; /* 设置白色边框，边框宽度为 2px */
            border-radius: 2px; /* 设置边框圆角，圆角半径为 10px */
            padding: 2px; /* 设置内边距，让输入的文字与边框有一定距离 */
            background-color: transparent; /* 设置背景透明 */
            color: white; /* 设置输入文字颜色为白色 */</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QPushButton" name="Fs">
       <property name="geometry">
        <rect>
         <x>290</x>
         <y>50</y>
         <width>151</width>
         <height>61</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
     <widget class="QPushButton" name="Operate">
      <property name="geometry">
       <rect>
        <x>720</x>
        <y>290</y>
        <width>180</width>
        <height>80</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="Certain">
      <property name="geometry">
       <rect>
        <x>720</x>
        <y>630</y>
        <width>180</width>
        <height>80</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="Init">
      <property name="geometry">
       <rect>
        <x>720</x>
        <y>460</y>
        <width>180</width>
        <height>80</height>
       </rect>
      </property>
      <property name="accessibleName">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;br/&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
      <property name="styleSheet">
       <string notr="true">background-repeat: no-repeat;
background-position: center;
background-size: cover;</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QCheckBox" name="ControlAir">
      <property name="geometry">
       <rect>
        <x>590</x>
        <y>170</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 37px;
    height: 37px;
    background-image: url('res/unchecked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

QCheckBox::indicator:checked {
    background-image: url('res/checked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QCheckBox" name="ControlTube">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>470</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 37px;
    height: 37px;
    background-image: url('res/unchecked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

QCheckBox::indicator:checked {
    background-image: url('res/checked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QCheckBox" name="ControlP">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>610</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 37px;
    height: 37px;
    background-image: url('res/unchecked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

QCheckBox::indicator:checked {
    background-image: url('res/checked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QGroupBox" name="NTBox">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>10</y>
        <width>521</width>
        <height>161</height>
       </rect>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
    background-image: url('res/ltw(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QLineEdit" name="Sum">
       <property name="geometry">
        <rect>
         <x>73</x>
         <y>78</y>
         <width>28</width>
         <height>21</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true"> border: 0.5px solid white; /* 设置白色边框，边框宽度为 2px */
            border-radius: 2px; /* 设置边框圆角，圆角半径为 10px */
            padding: 2px; /* 设置内边距，让输入的文字与边框有一定距离 */
            background-color: transparent; /* 设置背景透明 */
            color: white; /* 设置输入文字颜色为白色 */</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
      <widget class="QWidget" name="Circle" native="true">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="geometry">
        <rect>
         <x>125</x>
         <y>65</y>
         <width>251</width>
         <height>51</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="ControlTd">
       <property name="geometry">
        <rect>
         <x>385</x>
         <y>63</y>
         <width>121</width>
         <height>51</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
     <widget class="QCheckBox" name="ControlNT">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>0</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 37px;
    height: 37px;
    background-image: url('res/unchecked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

QCheckBox::indicator:checked {
    background-image: url('res/checked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QCheckBox" name="ControlEle">
      <property name="geometry">
       <rect>
        <x>590</x>
        <y>330</y>
        <width>41</width>
        <height>41</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QCheckBox::indicator {
    width: 37px;
    height: 37px;
    background-image: url('res/unchecked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

QCheckBox::indicator:checked {
    background-image: url('res/checked.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QGroupBox" name="TubeQuality">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>470</y>
        <width>511</width>
        <height>141</height>
       </rect>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
    background-image: url('res/cbjkf(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QPushButton" name="CBj">
       <property name="geometry">
        <rect>
         <x>155</x>
         <y>55</y>
         <width>191</width>
         <height>61</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="PowerBox">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>610</y>
        <width>511</width>
        <height>131</height>
       </rect>
      </property>
      <property name="mouseTracking">
       <bool>false</bool>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
    background-image: url('res/yuan(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string/>
      </property>
      <widget class="QPushButton" name="Szchoice">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>50</y>
         <width>191</width>
         <height>61</height>
        </rect>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox">
      <property name="geometry">
       <rect>
        <x>810</x>
        <y>20</y>
        <width>281</width>
        <height>221</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">QGroupBox {
    width: 100px;
    height: 100px;
    background-image: url('res/ats(1).png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}</string>
      </property>
      <property name="title">
       <string>GroupBox</string>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="page_7">
     <layout class="QHBoxLayout" name="horizontalLayout_8">
      <item>
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>性能评估</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QPushButton" name="btnMinimize">
    <property name="geometry">
     <rect>
      <x>1540</x>
      <y>0</y>
      <width>30</width>
      <height>20</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>30</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>30</width>
      <height>30</height>
     </size>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/icon/res/minimize.png</normaloff>:/icon/res/minimize.png</iconset>
    </property>
   </widget>
   <widget class="QPushButton" name="btnClose">
    <property name="geometry">
     <rect>
      <x>1570</x>
      <y>0</y>
      <width>30</width>
      <height>20</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>30</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>30</width>
      <height>30</height>
     </size>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset resource="resource.qrc">
      <normaloff>:/icon/res/close.png</normaloff>:/icon/res/close.png</iconset>
    </property>
   </widget>
   <widget class="QLabel" name="timeLabel">
    <property name="geometry">
     <rect>
      <x>400</x>
      <y>5</y>
      <width>400</width>
      <height>25</height>
     </rect>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;br/&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
   </widget>
   <zorder>stackedWidget</zorder>
   <zorder>frame_2</zorder>
   <zorder>frame_3</zorder>
   <zorder>frame</zorder>
   <zorder>btnMinimize</zorder>
   <zorder>btnClose</zorder>
   <zorder>timeLabel</zorder>
  </widget>
 </widget>
 <resources>
  <include location="resource.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>btnMinimize</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>showMinimized()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>895</x>
     <y>19</y>
    </hint>
    <hint type="destinationlabel">
     <x>862</x>
     <y>-34</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btnClose</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>948</x>
     <y>29</y>
    </hint>
    <hint type="destinationlabel">
     <x>1011</x>
     <y>25</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
