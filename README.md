# 导弹发射顺序优化系统

本项目实现了一个导弹发射顺序优化系统，能够基于"一前一后、一左一右"的评估原则，计算最优的导弹发射序列。系统会在一次运行中同时计算并显示W=3和W=5两种评估窗口大小的结果，并进行比较，其中W=5是通过高效的匹配链接算法基于W=3数据集构建的。

## 功能特点

- 支持4、8、12、16、32枚导弹的发射顺序优化
- 同时计算并显示W=3和W=5两种窗口评估的结果
- 可指定首发导弹
- 高效的W=5数据集构建算法，秒级生成大规模组合
- 详细的评分分析和结果导出
- **交互式参数设置**：可在运行时动态调整关键算法参数
- **结果比较**：自动比较W=3和W=5评估结果的差异
- **用户友好**：运行时提示用户输入关键参数，如不指定则使用默认值

## 文件说明

- `missile_launch_evaluator.py`: W=3评估器，实现了基础的导弹发射评分算法
- `missile_launch_w5_generator.py`: W=5数据集生成器，基于W=3数据集高效构建W=5组合
- `main.py`: 主程序，整合了W=3和W=5的评估功能，提供命令行接口

## 安装依赖

```bash
pip install numpy pandas tqdm
```

## 使用方法

### 基本用法

```bash
python main.py
```

运行程序后，系统会提示您输入以下关键参数：
1. **导弹数量**：可选4、8、12、16或32，默认为16
2. **首发导弹编号**：1到导弹总数之间的整数，直接回车表示不指定
3. **W=3组合选择比例**：0-1之间的浮点数，默认为0.2（即20%）

如果不想使用交互式输入，可以通过命令行参数直接指定：

```bash
python main.py -n 16 -f 1 --top_percent 0.2
```

### 交互式参数设置

使用 `--interactive` 参数可在运行时交互式设置更多W=5算法的参数：

```bash
python main.py --interactive
```

交互式设置包括以下参数：
- 选择前多少比例的W=3组合 (默认0.2，即20%)
- 第一个W=3子序列权重 (默认0.35)
- 中间W=3子序列权重 (默认0.30)
- 最后一个W=3子序列权重 (默认0.35)
- 并行处理的进程数 (默认使用CPU核心数)
- 是否使用缓存 (默认是)

### 命令行参数

- `-n, --num_missiles`: 导弹数量，可选值为4、8、12、16或32，默认在运行时询问
- `-f, --first_missile`: 指定首发导弹编号，默认在运行时询问
- `--top_percent`: 选择前多少比例的W=3组合，默认在运行时询问
- `--force_recalc`: 强制重新计算，不使用缓存
- `--analyze_only`: 仅分析已有结果，不计算最优序列
- `--top_n`: 显示前多少个高分组合，默认为10
- `--interactive`: 交互式输入参数模式

### 示例

1. 使用默认参数计算16枚导弹的最优序列，运行时交互输入参数：

```bash
python main.py
```

2. 指定首发导弹为1：

```bash
python main.py -f 1
```

3. 仅分析已有结果，不计算最优序列：

```bash
python main.py --analyze_only
```

4. 强制重新计算数据集，并使用交互式参数设置：

```bash
python main.py --force_recalc --interactive
```

5. 直接运行W=5生成器，使用交互式参数设置：

```bash
python missile_launch_w5_generator.py
```

## 输出内容说明

程序运行后将依次输出：

1. **W=3评估结果**：
   - W=3评分最高的组合列表
   - W=3评估下的最优发射序列及得分

2. **W=5评估结果**：
   - W=5评分最高的组合列表
   - W=5评估下的最优发射序列及得分

3. **结果比较**：
   - W=3与W=5的平均得分对比
   - 两种序列的相似度分析

## W=5数据集构建算法

W=5数据集构建采用了以下优化策略：

1. **索引化匹配链接**：构建了两种高效索引结构
   - `pair_to_third`: 从导弹对(a,b)映射到可能的第三枚导弹c
   - `overlap_index`: 从重叠对(b,c)映射到可能的前导或后续导弹

2. **分层筛选**：只选择前20%的高分W=3组合作为起点

3. **并行批处理**：利用多进程并行处理，充分利用多核CPU

4. **缓存机制**：支持结果缓存，避免重复计算

这些优化策略使W=5数据集的构建时间从几小时缩短到秒级，同时保持了解的质量。

## 可调参数说明

W=5算法提供以下可调参数，影响结果质量和性能：

1. **top_percent**：选择前多少比例的W=3组合作为起点
   - 值越小，速度越快，但可能降低解的质量
   - 值越大，速度越慢，但可能提高解的质量
   - 推荐范围：0.1~0.3

2. **alpha权重**：三个W=3子序列的权重分配
   - alpha1：第一个W=3子序列权重
   - alpha2：中间W=3子序列权重
   - alpha3：最后一个W=3子序列权重
   - 权重总和应为1.0（自动归一化）

3. **进程数**：并行处理使用的进程数
   - 默认使用所有CPU核心
   - 可根据系统资源调整

## 输出文件

- `missile_launch_w3_scores_n{n}.xlsx`: W=3评分结果
- `missile_launch_w5_scores_n{n}.xlsx`: W=5评分结果
- `optimal_launch_sequence_n{n}_w3.txt`: W=3评估的最优发射序列
- `optimal_launch_sequence_n{n}_w5.txt`: W=5评估的最优发射序列
- `missile_launch_w5_scores_n{n}_cache.pkl`: W=5数据集缓存文件

## 评分原理

评分系统基于以下原则：

1. **距离评分**：优先选择行距较远的导弹
2. **交替评分**：优先左右交替发射
3. **多样性评分**：优先选择空间分布更均匀的组合

W=5评分通过加权平均W=3子序列的评分计算得出：
- 第一个W=3子序列权重: 0.35
- 中间W=3子序列权重: 0.30
- 最后一个W=3子序列权重: 0.35 