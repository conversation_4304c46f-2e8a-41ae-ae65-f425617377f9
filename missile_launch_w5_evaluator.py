import numpy as np
import itertools
import pandas as pd
from tqdm import tqdm
import math
import os
from missile_launch_evaluator import MissileLaunchEvaluator

class MissileLaunchW5Evaluator:
    def __init__(self, num_missiles=16):
        """初始化W=5导弹发射评估器
        
        Args:
            num_missiles: 导弹总数，可取值为4、8、12、16、32
        """
        # 确保导弹数量有效
        assert num_missiles in [8, 12, 16, 32], "W=5模式下导弹数量必须为8、12、16或32"
        
        self.total_missiles = num_missiles
        # 创建原始的W=3评估器用于子序列评分
        self.w3_evaluator = MissileLaunchEvaluator(num_missiles=num_missiles)
        
        # 加权融合的权重参数
        self.alpha1 = 0.35  # 第一个子序列权重（包含边缘导弹1）
        self.alpha2 = 0.30  # 中间子序列权重
        self.alpha3 = 0.35  # 最后一个子序列权重（包含边缘导弹5）
        
        # 非线性映射参数
        self.sigmoid_k = 6.0    # Sigmoid曲线陡度
        self.sigmoid_x0 = 0.5   # Sigmoid中心点
        self.min_score = 0.9    # 最低分数阈值
        
        # 全局特征权重
        self.global_weight = 0.2  # 全局特征在总分中的比例
        
    def compute_w3_scores(self):
        """计算所有W=3组合的评分"""
        return self.w3_evaluator.compute_all_combinations()
        
    def compute_global_feature_score(self, missile_sequence):
        """计算五枚导弹序列的全局特征评分
        
        Args:
            missile_sequence: 五枚导弹的序列，如(2,8,1,6,3)
            
        Returns:
            全局特征评分
        """
        if len(missile_sequence) != 5:
            return 0.9  # 默认分数
            
        # 1. 位置分布多样性 - 检查行列分布
        rows = [self.w3_evaluator.get_row(m) for m in missile_sequence]
        cols = [self.w3_evaluator.get_col(m) for m in missile_sequence]
        
        # 计算行的分散程度 - 使用标准差
        row_std = np.std(rows)
        # 标准化到[0,1]区间，考虑最大可能标准差
        max_row_std = np.std([0, self.w3_evaluator.rows-1, 0, self.w3_evaluator.rows-1, 0])
        if max_row_std > 0:
            row_diversity = min(row_std / max_row_std, 1.0)
        else:
            row_diversity = 0.5
            
        # 列多样性 - 检查左右交替
        col_changes = sum(1 for i in range(1, 5) if cols[i] != cols[i-1])
        col_diversity = min(col_changes / 4, 1.0)  # 归一化
        
        # 2. 首尾导弹的距离关系
        a, e = missile_sequence[0], missile_sequence[4]
        first_last_distance = self.w3_evaluator.euclidean_distance(a, e)
        # 归一化距离评分
        max_distance = math.sqrt((self.w3_evaluator.rows-1)**2 + 25)  # 估计最大可能距离
        first_last_score = first_last_distance / max_distance if max_distance > 0 else 0.5
        
        # 3. 整体平衡性 - 左右分布
        left_count = sum(1 for m in missile_sequence if self.w3_evaluator.get_col(m) == 0)
        balance_score = 1.0 - abs(left_count/5 - 0.5)*2  # 偏离0.5越远，分数越低
        
        # 计算全局特征总分 - 加权组合
        global_score = (0.4 * row_diversity + 
                       0.3 * col_diversity + 
                       0.2 * first_last_score + 
                       0.1 * balance_score)
        
        # 映射到[0.9, 1.0]区间
        global_score = 0.9 + 0.1 * global_score
        
        return global_score
        
    def compute_w5_score(self, sequence, w3_results_dict):
        """计算W=5序列的评分，使用改进的匹配链接方法
        
        Args:
            sequence: 5枚导弹的序列，如(2,8,1,6,3)
            w3_results_dict: W=3评分结果的字典，键为三元组，值为得分
            
        Returns:
            W=5序列的综合评分
        """
        if len(sequence) != 5:
            return 0.9  # 默认分数
        
        # 提取三个子序列
        subseq1 = (sequence[0], sequence[1], sequence[2])  # 前三个导弹
        subseq2 = (sequence[1], sequence[2], sequence[3])  # 中间三个导弹
        subseq3 = (sequence[2], sequence[3], sequence[4])  # 后三个导弹
        
        # 查找子序列的W=3分数
        score1 = w3_results_dict.get(subseq1, 0.9)
        score2 = w3_results_dict.get(subseq2, 0.9)
        score3 = w3_results_dict.get(subseq3, 0.9)
        
        # 1. 加权融合子序列分数
        weighted_local_score = (self.alpha1 * score1 + 
                               self.alpha2 * score2 + 
                               self.alpha3 * score3)
        
        # 2. 计算全局特征评分
        global_score = self.compute_global_feature_score(sequence)
        
        # 3. 结合局部和全局评分
        combined_score = ((1 - self.global_weight) * weighted_local_score + 
                         self.global_weight * global_score)
        
        # 4. 非线性映射
        # 将分数映射到[min_score, 1.0]区间
        sigmoid_value = 1.0 / (1.0 + math.exp(-self.sigmoid_k * (combined_score - self.sigmoid_x0)))
        final_score = self.min_score + (1.0 - self.min_score) * sigmoid_value
        
        return final_score
    
    def compute_all_w5_combinations(self):
        """计算所有可能的W=5导弹组合及其评分"""
        # 首先获取所有W=3的评分结果
        w3_results = self.compute_w3_scores()
        
        # 将W=3结果转换为字典形式，便于快速查找
        w3_results_dict = {group: score for (group, score) in w3_results}
        
        # 生成所有可能的5发导弹组合
        missile_ids = list(range(1, self.total_missiles + 1))
        all_permutations = list(itertools.permutations(missile_ids, 5))
        
        results = []
        print(f"计算{len(all_permutations)}个可能的W=5导弹组合评分...")
        
        for perm in tqdm(all_permutations):
            score = self.compute_w5_score(perm, w3_results_dict)
            results.append((perm, score))
        
        # 按照导弹编号从小到大排序
        results.sort(key=lambda x: x[0])
        return results
    
    def save_to_excel(self, results, output_file="missile_launch_w5_scores.xlsx"):
        """将W=5评分结果保存到Excel文件"""
        # 创建数据框
        data = {
            "发射顺序": ["-".join(str(m) for m in group) for (group, _) in results],
            "评分": [score for _, score in results]
        }
        df = pd.DataFrame(data)
        
        # 保存到Excel
        df.to_excel(output_file, index=False)
        print(f"W=5结果已保存到 {output_file}")
    
    def find_optimal_launch_sequence(self, w5_results, first_missile=None):
        """找出所有导弹的最优发射顺序 - 使用W=5数据
        
        Args:
            w5_results: 所有W=5导弹组合及评分的列表
            first_missile: 用户指定的首发导弹编号
            
        Returns:
            最优发射序列和总得分
        """
        # 构建W=5查找表
        w5_index = {}
        for (group, score) in w5_results:
            group_key = tuple(group)
            w5_index[group_key] = score
        
        # 创建导弹使用状态记录
        n = self.total_missiles
        used_missiles = set()
        optimal_sequence = []
        group_scores = []
        
        # 处理首发导弹
        if first_missile is not None:
            if 1 <= first_missile <= n:
                optimal_sequence.append(first_missile)
                used_missiles.add(first_missile)
                print(f"已指定导弹 {first_missile} 为首发导弹")
        
        # 贪心算法寻找最优序列
        while len(used_missiles) < n:
            best_group = None
            best_score = -1
            
            # 确定下一组W=5的起始点
            start_idx = len(optimal_sequence) - 4 if len(optimal_sequence) >= 4 else 0
            if start_idx < 0:
                start_idx = 0
                
            # 如果已经有序列，考虑重叠
            prefix = tuple(optimal_sequence[start_idx:]) if start_idx < len(optimal_sequence) else ()
            
            # 寻找最佳的下一组
            for (group, score) in w5_results:
                # 检查是否与现有序列有重叠，且不包含已使用的导弹
                can_use = True
                overlap_len = min(len(prefix), len(group))
                
                # 验证重叠部分是否匹配
                if overlap_len > 0:
                    if prefix[-overlap_len:] != group[:overlap_len]:
                        can_use = False
                
                # 检查新增的导弹是否已使用
                new_missiles = group[overlap_len:]
                for m in new_missiles:
                    if m in used_missiles:
                        can_use = False
                        break
                
                if can_use and score > best_score:
                    best_group = group
                    best_score = score
            
            # 如果找到最佳组合，添加到序列中
            if best_group:
                # 确定要添加的新导弹
                if len(prefix) > 0:
                    overlap_len = min(len(prefix), len(best_group))
                    new_missiles = best_group[overlap_len:]
                else:
                    new_missiles = best_group
                    
                # 添加新导弹到序列
                for m in new_missiles:
                    if m not in used_missiles:
                        optimal_sequence.append(m)
                        used_missiles.add(m)
                
                # 记录这个W=5组的得分
                group_scores.append(best_score)
            else:
                # 如果没有找到合适的组合，选择任意未使用的导弹
                available = [m for m in range(1, n+1) if m not in used_missiles]
                if available:
                    next_missile = min(available)  # 选择编号最小的未使用导弹
                    optimal_sequence.append(next_missile)
                    used_missiles.add(next_missile)
                else:
                    break
        
        # 计算平均得分
        avg_score = sum(group_scores) / len(group_scores) if group_scores else 0.9
        
        return optimal_sequence, avg_score, group_scores
            

def main():
    """主函数，处理用户输入并运行W=5评估"""
    valid_counts = [8, 12, 16, 32]
    
    while True:
        try:
            n = int(input(f"请输入导弹数量(W=5模式下可选{valid_counts}): "))
            if n in valid_counts:
                break
            else:
                print(f"请输入有效的导弹数量: {valid_counts}")
        except ValueError:
            print("请输入有效的数字")
    
    # 创建W=5评估器
    evaluator = MissileLaunchW5Evaluator(num_missiles=n)
    
    # 询问是否要指定首发导弹
    first_missile = None
    while True:
        specify_first = input("是否要指定首发导弹？(y/n): ").strip().lower()
        if specify_first == 'y':
            try:
                first_missile_input = input(f"请输入首发导弹编号(1-{n})，或输入0取消: ")
                first_missile = int(first_missile_input)
                if first_missile == 0:
                    first_missile = None
                    print("已取消首发导弹指定")
                    break
                elif 1 <= first_missile <= n:
                    print(f"已指定导弹{first_missile}为首发导弹")
                    break
                else:
                    print(f"请输入有效的导弹编号(1-{n})或0取消")
            except ValueError:
                print("请输入有效的数字")
        elif specify_first == 'n':
            break
        else:
            print("请输入 y 或 n")
    
    # 计算所有W=5组合
    w5_results_file = f"missile_launch_w5_scores_n{n}.xlsx"
    # 检查W=5结果文件是否已存在
    if os.path.exists(w5_results_file):
        use_existing = input(f"发现已有W=5评分文件 {w5_results_file}，是否使用现有文件？(y/n): ").strip().lower()
        if use_existing == 'y':
            # 从Excel加载W=5结果
            df = pd.read_excel(w5_results_file)
            w5_results = []
            for _, row in df.iterrows():
                sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
                score = row['评分']
                w5_results.append((sequence, score))
        else:
            # 重新计算W=5结果
            w5_results = evaluator.compute_all_w5_combinations()
            evaluator.save_to_excel(w5_results, w5_results_file)
    else:
        # 计算并保存W=5结果
        w5_results = evaluator.compute_all_w5_combinations()
        evaluator.save_to_excel(w5_results, w5_results_file)
    
    # 显示Top 5最高分组合
    print("\nW=5评分最高的前5个发射组合:")
    top_5 = sorted(w5_results, key=lambda x: x[1], reverse=True)[:5]
    for i, (group, score) in enumerate(top_5):
        group_str = "-".join(str(m) for m in group)
        print(f"组合 {i+1}: {group_str}, 得分: {score:.4f}")
    
    # 寻找最优发射序列
    print("\n计算W=5最优发射序列...")
    optimal_sequence, avg_score, group_scores = evaluator.find_optimal_launch_sequence(w5_results, first_missile)
    
    # 文件名添加首发导弹信息
    first_missile_suffix = f"_first{first_missile}" if first_missile else ""
    
    # 打印最优序列
    print(f"\n最优发射序列 ({n}发导弹):")
    sequence_str = "-".join(str(m) for m in optimal_sequence)
    print(sequence_str)
    print(f"W=5平均得分: {avg_score:.4f}")
    
    # 保存最优序列到文件
    sequence_file = f"optimal_launch_w5_sequence_n{n}{first_missile_suffix}.txt"
    with open(sequence_file, "w") as f:
        if first_missile:
            f.write(f"W=5最优发射序列 ({n}发导弹, 首发导弹: {first_missile}):\n")
        else:
            f.write(f"W=5最优发射序列 ({n}发导弹):\n")
        f.write(f"{sequence_str}\n")
        f.write(f"W=5平均得分: {avg_score:.4f}\n")
        
        # 单独添加首发导弹信息
        if first_missile:
            f.write(f"首发导弹: {first_missile}\n")
        else:
            f.write("首发导弹: 未指定\n")
        
        # 打印W=5组的详细分解
        f.write("\n按W=5一组的详细分解:\n")
        idx = 0
        for i in range(0, len(optimal_sequence), 5):
            if i+4 < len(optimal_sequence):
                group = optimal_sequence[i:i+5]
                group_str = "-".join(str(m) for m in group)
                if idx < len(group_scores):
                    f.write(f"组 {i//5+1}: {group_str}, 得分: {group_scores[idx]:.4f}\n")
                    idx += 1
                else:
                    f.write(f"组 {i//5+1}: {group_str}\n")
            else:
                # 处理最后不足5发的情况
                group = optimal_sequence[i:]
                group_str = "-".join(str(m) for m in group)
                f.write(f"组 {i//5+1}: {group_str}\n")
    
    print(f"W=5最优发射序列已保存到 {sequence_file}")

if __name__ == "__main__":
    main() 