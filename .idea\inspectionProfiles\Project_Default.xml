<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="27">
            <item index="0" class="java.lang.String" itemvalue="contexttimer" />
            <item index="1" class="java.lang.String" itemvalue="scikit-image" />
            <item index="2" class="java.lang.String" itemvalue="editdistance" />
            <item index="3" class="java.lang.String" itemvalue="jieba" />
            <item index="4" class="java.lang.String" itemvalue="nltk" />
            <item index="5" class="java.lang.String" itemvalue="pre-commit" />
            <item index="6" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="7" class="java.lang.String" itemvalue="python-magic" />
            <item index="8" class="java.lang.String" itemvalue="ftfy" />
            <item index="9" class="java.lang.String" itemvalue="pycocoevalcap" />
            <item index="10" class="java.lang.String" itemvalue="spacy" />
            <item index="11" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="12" class="java.lang.String" itemvalue="opendatasets" />
            <item index="13" class="java.lang.String" itemvalue="transformers" />
            <item index="14" class="java.lang.String" itemvalue="timm" />
            <item index="15" class="java.lang.String" itemvalue="streamlit" />
            <item index="16" class="java.lang.String" itemvalue="ipython" />
            <item index="17" class="java.lang.String" itemvalue="plotly" />
            <item index="18" class="java.lang.String" itemvalue="omegaconf" />
            <item index="19" class="java.lang.String" itemvalue="pandas" />
            <item index="20" class="java.lang.String" itemvalue="tqdm" />
            <item index="21" class="java.lang.String" itemvalue="fairscale" />
            <item index="22" class="java.lang.String" itemvalue="iopath" />
            <item index="23" class="java.lang.String" itemvalue="webdataset" />
            <item index="24" class="java.lang.String" itemvalue="einops" />
            <item index="25" class="java.lang.String" itemvalue="decord" />
            <item index="26" class="java.lang.String" itemvalue="pycocotools" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>