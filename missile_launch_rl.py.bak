﻿import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque
import matplotlib.pyplot as plt
import time
import os
import pandas as pd  # 添加pandas导入，用于读取Excel文件
import sys

# 设置随机种子以确保结果可复现
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
torch.manual_seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

class DQN(nn.Module):
    """深度Q网络模型"""
    
    def __init__(self, state_size, action_size, hidden_size=128):
        super(DQN, self).__init__()
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, action_size)
        
    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        return self.fc3(x)

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)
        
    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))
        
    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)
    
    def __len__(self):
        return len(self.buffer)

class MissileLaunchEnvironment:
    """导弹发射环境模拟器"""
    
    # 添加类变量来跟踪是否已经打印过距离数据
    _distance_data_printed = False
    
    def __init__(self, n_missiles, first_missile_id=None, tdistance_path=None):
        """
        初始化导弹发射环境
        
        参数:
        - n_missiles: 导弹总数量，必须是偶数
        - first_missile_id: 首发导弹ID（可选）
        - tdistance_path: Tdistance.xlsx文件路径（可选）
        """
        assert n_missiles % 2 == 0, "导弹数量必须是偶数"
        assert n_missiles in [4, 8, 12, 16, 32], "导弹数量必须为4, 8, 12, 16或32"
        
        self.n_missiles = n_missiles
        self.n_rows = n_missiles // 2
        self.tdistance_path = tdistance_path
        
        # 初始化导弹排布：奇数编号在左侧，偶数编号在右侧
        self.missiles = []
        for i in range(self.n_rows):
            # 左侧导弹，奇数ID
            self.missiles.append((2*i+1, i, 0))  # (id, row, side=0表示左侧)
            # 右侧导弹，偶数ID
            self.missiles.append((2*i+2, i, 1))  # (id, row, side=1表示右侧)
        
        # 加载导弹距离数据
        self.load_missile_distances()
        
        # 初始化状态
        self.reset(first_missile_id)
    
    def load_missile_distances(self):
        """从Tdistance.xlsx文件加载导弹间距离数据"""
        try:
            # 读取Excel文件
            if self.tdistance_path:
                df = pd.read_excel(self.tdistance_path)
            else:
                # 如果没有提供路径，使用默认路径
                df = pd.read_excel("Tdistance.xlsx")
            
            # 提取行距离值
            self.row_distances = []
            for i in range(1, self.n_rows):
                col_name = f'r{i}'
                if col_name in df.columns:
                    self.row_distances.append(float(df[col_name].iloc[0]))
                else:
                    # 如果找不到对应列，使用默认值1.0
                    if not MissileLaunchEnvironment._distance_data_printed:
                        print(f"警告：找不到{col_name}列，使用默认值1.0")
                    self.row_distances.append(1.0)
            
            # 提取列距离值（两列之间的距离）
            if 'l' in df.columns:
                self.column_distance = float(df['l'].iloc[0])
            else:
                # 如果找不到列距离，使用默认值1.0
                if not MissileLaunchEnvironment._distance_data_printed:
                    print("警告：找不到列距离(l)，使用默认值1.0")
                self.column_distance = 1.0
                
            # 只在第一次打印距离数据信息
            if not MissileLaunchEnvironment._distance_data_printed:
                print(f"成功加载导弹距离数据：行距离={self.row_distances}，列距离={self.column_distance}")
                MissileLaunchEnvironment._distance_data_printed = True
                
        except Exception as e:
            if not MissileLaunchEnvironment._distance_data_printed:
                print(f"加载导弹距离数据失败：{e}，使用默认值")
                MissileLaunchEnvironment._distance_data_printed = True
                
            # 使用默认值
            self.row_distances = [1.0] * (self.n_rows - 1)
            self.column_distance = 1.0
    
    def reset(self, first_missile_id=None):
        """重置环境状态"""
        # 未发射的导弹列表
        self.unfired_missiles = self.missiles.copy()
        # 已发射的导弹序列
        self.fired_sequence = []
        # 上一个发射位置 (row, side)
        self.last_position = None
        
        # 如果指定了首发导弹，则先发射该导弹
        if first_missile_id is not None:
            for i, missile in enumerate(self.unfired_missiles):
                if missile[0] == first_missile_id:
                    first_missile = self.unfired_missiles.pop(i)
                    self.fired_sequence.append(first_missile)
                    self.last_position = (first_missile[1], first_missile[2])
                    break
        
        # 返回初始状态
        return self._get_state()
    
    def _get_state(self):
        """获取当前状态向量表示"""
        # 创建导弹排布矩阵 (n_rows x 2)
        layout_matrix = np.zeros((self.n_rows, 2))
        
        # 标记未发射导弹的位置
        for missile_id, row, side in self.unfired_missiles:
            layout_matrix[row, side] = 1
        
        # 创建状态向量
        state = layout_matrix.flatten()  # 展平排布矩阵
        
        # 添加上一发射位置信息
        if self.last_position:
            last_row, last_side = self.last_position
            last_position_vec = np.zeros(self.n_rows * 2)
            last_position_vec[last_row * 2 + last_side] = 1
            state = np.concatenate([state, last_position_vec])
        else:
            # 如果没有上一发射位置，用零向量表示
            state = np.concatenate([state, np.zeros(self.n_rows * 2)])
        
        # 添加最近发射序列信息 (最多3个)
        recent_fired = []
        for i in range(min(3, len(self.fired_sequence))):
            missile = self.fired_sequence[-i-1]
            missile_vec = np.zeros(self.n_rows * 2)
            missile_vec[missile[1] * 2 + missile[2]] = 1
            recent_fired.append(missile_vec)
        
        # 补齐3个位置
        while len(recent_fired) < 3:
            recent_fired.append(np.zeros(self.n_rows * 2))
        
        # 合并成最终状态向量
        for vec in recent_fired:
            state = np.concatenate([state, vec])
        
        return state
    
    def euclidean_distance(self, pos1, pos2):
        """
        计算两个位置之间的距离，使用预加载的距离值
        
        参数:
        - pos1: 第一个导弹位置 (row, side)
        - pos2: 第二个导弹位置 (row, side)
        
        返回:
        - 两个导弹之间的距离
        """
        row1, side1 = pos1
        row2, side2 = pos2
        
        # 计算行距离
        row_dist = 0.0
        if row1 != row2:
            start_row = min(row1, row2)
            end_row = max(row1, row2)
            # 累加行之间的距离
            for r in range(start_row, end_row):
                if r < len(self.row_distances):
                    row_dist += self.row_distances[r]
                else:
                    row_dist += 1.0  # 默认距离
        
        # 计算列距离
        col_dist = 0.0
        if side1 != side2:
            col_dist = self.column_distance
        
        # 使用勾股定理计算总距离
        return np.sqrt(row_dist**2 + col_dist**2)
    
    def time_decay_factor(self, steps_ago):
        """计算时间衰减因子，越近的历史影响越大"""
        return np.exp(-0.5 * steps_ago)
        
    def calculate_diversity_reward(self, current_missile, max_lookback=5):
        """计算与历史发射导弹的多样性奖励"""
        if len(self.fired_sequence) < 2:
            return 1.0  # 发射序列过短，默认高奖励
        
        curr_row, curr_side = current_missile[1], current_missile[2]
        
        # 计算与历史发射导弹的空间多样性
        total_weight = 0
        weighted_diversity = 0
        
        # 只考虑最近的max_lookback枚导弹
        recent_missiles = self.fired_sequence[-max_lookback:] if len(self.fired_sequence) > max_lookback else self.fired_sequence
        
        for i, missile in enumerate(recent_missiles):
            steps_ago = len(recent_missiles) - i
            weight = self.time_decay_factor(steps_ago)
            
            # 计算空间距离
            m_row, m_side = missile[1], missile[2]
            distance = self.euclidean_distance((curr_row, curr_side), (m_row, m_side))
            
            # 距离越远，多样性越高
            diversity = min(1.0, distance / 4.0)  # 标准化到[0,1]区间
            
            weighted_diversity += weight * diversity
            total_weight += weight
        
        return weighted_diversity / total_weight if total_weight > 0 else 1.0
    
    def calculate_nearby_penalty(self, current_missile):
        """计算附近未发射导弹的惩罚"""
        penalty = 0.0
        curr_row, curr_side = current_missile[1], current_missile[2]
        
        for missile in self.unfired_missiles:
            m_row, m_side = missile[1], missile[2]
            distance = self.euclidean_distance((curr_row, curr_side), (m_row, m_side))
            
            # 如果有未发射的导弹在当前选择附近，增加惩罚
            if distance < 2.0:
                penalty += 0.2 * (1 - distance / 2.0)  # 距离越近，惩罚越重
        
        return min(0.8, penalty)  # 限制最大惩罚
    
    def is_valid_next_missile(self, missile, min_row_distance=3):
        """
        判断一个导弹是否适合作为下一个发射的导弹
        
        参数:
        - missile: 候选导弹 (id, row, side)
        - min_row_distance: 最小行距离要求(默认为3)
        
        返回:
        - valid: 是否有效
        - reason: 无效原因
        """
        if not self.last_position:
            # 首发导弹，没有限制
            return True, "首发导弹"
            
        last_row, last_side = self.last_position
        curr_row, curr_side = missile[1], missile[2]
        
        # 1. 距离检查
        distance = self.euclidean_distance((curr_row, curr_side), (last_row, last_side))
        
        # 行距离检查
        row_distance = abs(curr_row - last_row)
        
        # 行距离要求
        if row_distance < min_row_distance:
            return False, f"行距离不足{min_row_distance} (当前:{row_distance})"
            
        # 如果是相邻行的同侧，不允许发射
        if row_distance <= 1 and curr_side == last_side:
            return False, f"相邻行的同侧发射"
        
        # 2. 左右交替检查
        if curr_side == last_side:
            # 连续同侧发射检查
            same_side_count = 1
            for i in range(min(3, len(self.fired_sequence))):
                if self.fired_sequence[-(i+1)][2] == curr_side:
                    same_side_count += 1
                else:
                    break
            
            if same_side_count > 1:
                # 连续同侧发射超过1次
                return False, f"连续{same_side_count}次同侧发射"
        
        return True, "有效导弹"
    
    def get_valid_missiles(self, min_row_distance=3):
        """
        获取当前所有有效的导弹选择
        
        参数:
        - min_row_distance: 最小行距离要求(默认为3)
        
        返回:
        - 有效导弹列表，每项为(索引, 导弹, 基础分)
        """
        valid_missiles = []
        invalid_missiles = []
        
        for idx, missile in enumerate(self.unfired_missiles):
            valid, reason = self.is_valid_next_missile(missile, min_row_distance)
            if valid:
                valid_missiles.append((idx, missile, 1.0))  # (索引, 导弹, 基础分)
            else:
                # 即使无效，我们也记录它们，但会给较低的基础分
                invalid_missiles.append((idx, missile, 0.2))  # (索引, 导弹, 较低基础分)
        
        # 如果没有有效导弹，则降低标准再试一次
        if not valid_missiles and min_row_distance > 2:
            return self.get_valid_missiles(min_row_distance - 1)
            
        # 如果仍然没有有效导弹，使用所有无效导弹
        if not valid_missiles:
            return invalid_missiles
            
        return valid_missiles
        
    def step(self, action_idx):
        """
        执行一步导弹发射操作
        
        参数:
        - action_idx: 选择发射的导弹在unfired_missiles中的索引
        
        返回:
        - next_state: 下一个状态
        - reward: 获得的奖励
        - done: 是否结束
        - info: 额外信息
        """
        # 检查action是否有效
        if action_idx >= len(self.unfired_missiles):
            return self._get_state(), -10.0, False, {"error": "无效的行动"}
        
        # 执行发射
        missile = self.unfired_missiles.pop(action_idx)
        missile_id, row, side = missile
        self.fired_sequence.append(missile)
        
        # 计算即时奖励
        reward = 0
        
        if self.last_position:
            last_row, last_side = self.last_position
            
            # 检查是否是有效导弹
            is_valid, reason = self.is_valid_next_missile(missile)
            
            # 基础奖励分数
            if is_valid:
                # 有效导弹，给予高奖励
                base_reward = 1.0
            else:
                # 无效导弹，给予惩罚
                base_reward = -1.0
            
            # 1. 距离奖励: 使用真实欧几里得距离和行距离
            distance = self.euclidean_distance((row, side), (last_row, last_side))
            row_distance = abs(row - last_row)
            
            # 行距离评分 - 更关注行距离而非欧几里得距离
            if row_distance >= 5:
                distance_reward = 1.0  # 极好
            elif row_distance >= 4:
                distance_reward = 0.9  # 很好
            elif row_distance >= 3:
                distance_reward = 0.8  # 好
            elif row_distance >= 2:
                distance_reward = 0.5  # 一般
            else:
                distance_reward = 0.1  # 很差
            
            # 2. 交替奖励: 左右交替为1.0，同侧为负值奖励(-0.5)
            alternating_reward = 1.0 if side != last_side else -0.5
            
            # 3. 多样性奖励
            diversity_reward = self.calculate_diversity_reward(missile)
            
            # 组合即时奖励 - 以基础奖励为主导
            reward = base_reward * (
                0.5 * distance_reward + 
                0.3 * alternating_reward + 
                0.2 * diversity_reward
            )
        
        # 更新上一个发射位置
        self.last_position = (row, side)
        
        # 判断是否结束
        done = len(self.unfired_missiles) == 0
        
        # 终态奖励
        if done:
            # 计算整个序列的评分
            final_score = self._evaluate_sequence()
            reward += final_score * 2.0  # 增加终态奖励权重
        
        # 将奖励映射到[0.9, 1]区间
        reward = self._map_reward_to_range(reward)
        
        return self._get_state(), reward, done, {"missile_id": missile_id}
    
    def _map_reward_to_range(self, reward):
        """将奖励映射到[0.9, 1]区间"""
        # 使用改进的映射函数，确保在低分时更接近0.9
        # 对于负的reward值，给予更严格的惩罚
        if reward < 0:
            # 负奖励映射到[0.9, 0.93]
            return 0.9 + 0.03 / (1 + np.exp(2 * abs(reward)))
        else:
            # 正奖励映射到[0.93, 1.0]
            normalized = min(1.0, reward / 2.0)  # 将reward标准化到[0,1]区间
            return 0.93 + 0.07 * normalized
    
    def _evaluate_sequence(self):
        """评估完整的发射序列"""
        total_score = 0
        
        # 评估每相邻两枚导弹
        for i in range(1, len(self.fired_sequence)):
            prev_missile = self.fired_sequence[i-1]
            curr_missile = self.fired_sequence[i]
            
            # 使用欧几里得距离计算空间距离
            prev_row, prev_side = prev_missile[1], prev_missile[2]
            curr_row, curr_side = curr_missile[1], curr_missile[2]
            
            # 距离评分 - 与step函数保持一致的评分标准
            distance = self.euclidean_distance((curr_row, curr_side), (prev_row, prev_side))
            
            # 分段距离评分 - 更严格的标准
            if distance < 2.5:  # 距离太近
                distance_score = 0.1  # 严重惩罚
            elif distance < 4.0:  # 距离一般
                distance_score = 0.5  # 轻微惩罚
            else:  # 距离足够远
                distance_score = 1.0  # 满分
            
            # 交替评分 - 使用负值奖励
            alternating_score = 1.0 if curr_missile[2] != prev_missile[2] else -0.5
            
            # 组合评分 - 调整权重
            step_score = 0.6 * distance_score + 0.4 * alternating_score
            total_score += step_score
        
        # 检查整体序列的多样性
        row_counts = [0] * self.n_rows
        side_counts = [0, 0]  # [左侧计数, 右侧计数]
        
        for missile in self.fired_sequence:
            row, side = missile[1], missile[2]
            row_counts[row] += 1
            side_counts[side] += 1
        
        # 计算行分布均匀度
        max_row_count = max(row_counts)
        row_uniformity = 1.0 - (max_row_count - min(row_counts)) / len(self.fired_sequence)
        
        # 计算左右均衡度 - 更重视左右均衡
        side_balance = 1.0 - abs(side_counts[0] - side_counts[1]) / len(self.fired_sequence)
        
        # 检测连续同侧发射情况
        consecutive_same_side_penalty = 0
        consecutive_count = 1
        
        for i in range(1, len(self.fired_sequence)):
            if self.fired_sequence[i][2] == self.fired_sequence[i-1][2]:  # 如果连续同侧
                consecutive_count += 1
            else:
                consecutive_count = 1
            
            if consecutive_count > 1:
                # 惩罚随连续次数指数增长
                consecutive_same_side_penalty += 0.1 * (consecutive_count ** 2)
        
        # 归一化惩罚值
        normalized_penalty = min(1.0, consecutive_same_side_penalty / 10.0)
        
        # 综合序列多样性
        sequence_diversity = 0.6 * row_uniformity + 0.4 * side_balance
        
        # 计算最终评分 (局部评分 + 全局多样性 - 连续同侧惩罚)
        avg_step_score = total_score / (len(self.fired_sequence) - 1) if len(self.fired_sequence) > 1 else 0
        final_score = 0.6 * avg_step_score + 0.4 * sequence_diversity - normalized_penalty
        
        # 确保分数在[0.9, 1]之间
        return self._map_reward_to_range(final_score)
    
    def get_valid_actions(self):
        """获取当前可用的动作集"""
        return list(range(len(self.unfired_missiles)))
    
    def get_missile_id_from_action(self, action_idx):
        """从动作索引获取对应的导弹ID"""
        if 0 <= action_idx < len(self.unfired_missiles):
            return self.unfired_missiles[action_idx][0]
        return None
    
    def get_final_sequence(self):
        """获取最终的导弹发射序列"""
        return [missile[0] for missile in self.fired_sequence]
    
    def get_final_score(self):
        """获取最终的发射序列评分"""
        return self._evaluate_sequence()

class MissileLaunchDQNAgent:
    """基于DQN的导弹发射顺序优化智能体"""
    
    def __init__(self, state_size, action_size, hidden_size=128):
        self.state_size = state_size
        self.action_size = action_size
        
        # Q网络
        self.q_network = DQN(state_size, action_size, hidden_size)
        self.target_network = DQN(state_size, action_size, hidden_size)
        self.update_target_network()
        
        # 优化器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=0.001)
        self.loss_fn = nn.MSELoss()
        
        # 经验回放
        self.memory = ReplayBuffer(capacity=10000)
        
        # 超参数
        self.batch_size = 64
        self.gamma = 0.99  # 折扣因子
        self.epsilon = 1.0  # 探索率初始值
        self.epsilon_min = 0.01  # 探索率最小值
        self.epsilon_decay = 0.995  # 探索率衰减因子
        
        # 跟踪训练指标
        self.losses = []
    
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def select_action(self, state, env):
        """选择动作"""
        # 获取有效导弹列表
        valid_missiles = env.get_valid_missiles()
        
        if not valid_missiles:
            return None
        
        # 探索: 根据导弹有效性加权随机选择
        if np.random.rand() < self.epsilon:
            # 根据基础分数进行加权选择
            weights = [score for _, _, score in valid_missiles]
            indices = [idx for idx, _, _ in valid_missiles]
            
            # 加权随机选择
            total_weight = sum(weights)
            normalized_weights = [w / total_weight for w in weights]
            
            return np.random.choice(indices, p=normalized_weights)
        
        # 利用: 选择Q值最大的动作，但只考虑有效导弹
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.q_network(state_tensor).squeeze()
        
        # 只考虑有效导弹，并根据基础分数加权
        best_action = None
        best_score = float('-inf')
        
        for idx, _, base_score in valid_missiles:
            if idx < len(q_values):
                # 有效导弹的Q值乘以基础分数
                weighted_q = q_values[idx].item() * base_score
                if weighted_q > best_score:
                    best_score = weighted_q
                    best_action = idx
        
        return best_action
    
    def train(self, env, episodes=500, max_steps=100, update_freq=10, print_freq=50):
        """训练智能体"""
        best_reward = float('-inf')
        best_sequence = None
        first_missile_id = None
        
        # 如果有首发导弹，记录下来
        if env.fired_sequence:
            first_missile_id = env.fired_sequence[0][0]
        
        for episode in range(1, episodes + 1):
            state = env.reset(first_missile_id)  # 确保每次重置都使用相同的首发导弹
            total_reward = 0
            done = False
            steps = 0
            
            while not done and steps < max_steps:
                action = self.select_action(state, env)
                
                if action is None:
                    break
                
                next_state, reward, done, _ = env.step(action)
                total_reward += reward
                
                # 存储经验
                self.memory.add(state, action, reward, next_state, done)
                
                # 训练网络
                if len(self.memory) >= self.batch_size:
                    self._train_step()
                
                state = next_state
                steps += 1
                
                # 定期更新目标网络
                if steps % update_freq == 0:
                    self.update_target_network()
            
            # 衰减探索率
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
            
            # 保存最佳序列
            if total_reward > best_reward and steps > 0:
                best_reward = total_reward
                best_sequence = env.get_final_sequence()
                
            # 验证序列有效性
            valid_sequence = True
            test_env = MissileLaunchEnvironment(env.n_missiles)
            
            for i in range(1, len(best_sequence)):
                # 找到当前导弹在未发射列表中的索引
                curr_id = best_sequence[i]
                idx = None
                for j, missile in enumerate(test_env.unfired_missiles):
                    if missile[0] == curr_id:
                        idx = j
                        break
                
                if idx is not None:
                    # 检查这个导弹是否有效 (使用标准行距离要求3)
                    missile = test_env.unfired_missiles[idx]
                    valid, reason = test_env.is_valid_next_missile(missile, min_row_distance=3)
                    
                    if not valid:
                        valid_sequence = False
                        break
                    
                    # 发射这个导弹
                    test_env.step(idx)
            
            # 如果序列无效，不更新最佳序列
            if not valid_sequence:
                best_sequence = None
                best_reward = float('-inf')
            
            # 打印训练进度
            if episode % print_freq == 0:
                avg_reward = total_reward/steps if steps > 0 else 0
                print(f"Episode {episode}/{episodes}, Average Reward: {avg_reward:.4f}, Epsilon: {self.epsilon:.4f}")
        
        # 如果没有找到有效序列，返回当前环境的序列
        if best_sequence is None:
            print("未找到严格有效序列，使用最后一轮训练的序列...")
            best_sequence = env.get_final_sequence()
        
        # 返回训练后的最佳序列
        return best_sequence, env.get_final_score()
    
    def _train_step(self):
        """执行一步网络训练"""
        # 从经验回放中采样
        batch = self.memory.sample(self.batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        # 转换为张量
        states = torch.FloatTensor(np.array(states))
        actions = torch.LongTensor(actions).unsqueeze(1)
        rewards = torch.FloatTensor(rewards).unsqueeze(1)
        next_states = torch.FloatTensor(np.array(next_states))
        dones = torch.FloatTensor(dones).unsqueeze(1)
        
        # 计算当前Q值
        current_q = self.q_network(states).gather(1, actions)
        
        # 计算目标Q值
        with torch.no_grad():
            max_next_q = self.target_network(next_states).max(1)[0].unsqueeze(1)
            target_q = rewards + self.gamma * max_next_q * (1 - dones)
        
        # 计算损失
        loss = self.loss_fn(current_q, target_q)
        self.losses.append(loss.item())
        
        # 优化模型
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
    
    def plot_training_loss(self):
        """绘制训练损失曲线"""
        plt.figure(figsize=(10, 5))
        plt.plot(self.losses)
        plt.title("DQN Training Loss")
        plt.xlabel("Training Steps")
        plt.ylabel("Loss")
        plt.show()
    
    def save_model(self, path=None, n_missiles=None):
        """保存模型"""
        if path is None and n_missiles is not None:
            path = f"missile_dqn_model_{n_missiles}.pth"
        elif path is None:
            path = "missile_dqn_model.pth"
        torch.save(self.q_network.state_dict(), path)
    
    def load_model(self, path=None, n_missiles=None):
        """加载模型"""
        if path is None and n_missiles is not None:
            path = f"missile_dqn_model_{n_missiles}.pth"
        elif path is None:
            path = "missile_dqn_model.pth"
        self.q_network.load_state_dict(torch.load(path))
        self.update_target_network()

def find_optimal_sequence_rl(n_missiles, first_missile_id=None, train_mode=True, tdistance_path=None):
    """使用强化学习找到最优发射序列"""
    # 创建环境
    env = MissileLaunchEnvironment(n_missiles, first_missile_id, tdistance_path)
    
    # 计算状态空间和动作空间大小
    state_size = env._get_state().shape[0]
    action_size = n_missiles
    
    # 创建智能体
    agent = MissileLaunchDQNAgent(state_size, action_size, hidden_size=256)
    
    # 如果不是训练模式且有预训练模型，则加载
    if not train_mode:
        try:
            agent.load_model(n_missiles=n_missiles)
            print(f"加载预训练模型 missile_dqn_model_{n_missiles}.pth 成功!")
        except:
            print(f"无法加载导弹数量为{n_missiles}的预训练模型，将使用未训练的模型...")
    
    if train_mode:
        # 训练模式：训练并保存模型
        print(f"开始训练 {n_missiles} 枚导弹的发射序列优化...")
        start_time = time.time()
        best_sequence, best_score = agent.train(env, episodes=1000, max_steps=n_missiles, update_freq=10, print_freq=100)
        end_time = time.time()
        print(f"训练完成，耗时: {end_time - start_time:.2f}秒")
        
        # 保存训练好的模型
        agent.save_model(n_missiles=n_missiles)
        
        # 绘制训练损失曲线
        agent.plot_training_loss()
    else:
        # 评估模式：使用预训练模型推断
        print(f"使用预训练模型推断 {n_missiles} 枚导弹的最优发射序列...")
        
        # 初始化环境状态
        state = env.reset(first_missile_id)
        done = False
        
        # 按照训练好的策略逐步选择动作
        while not done:
            # 关闭探索，纯粹利用学习到的策略
            agent.epsilon = 0
            action = agent.select_action(state, env)
            
            if action is None:
                break
                
            state, _, done, _ = env.step(action)
        
        best_sequence = env.get_final_sequence()
        best_score = env.get_final_score()
    
    # 验证首发导弹正确性
    if first_missile_id is not None:
        # 确保序列的第一个元素是指定的首发导弹
        if best_sequence and best_sequence[0] != first_missile_id:
            # 如果不是，则强制将首发导弹放在第一位
            print(f"注意: 强制将导弹 {first_missile_id} 设为首发导弹")
            # 移除首发导弹ID（如果已在序列中）
            if first_missile_id in best_sequence:
                best_sequence.remove(first_missile_id)
            # 将首发导弹ID插入到序列首位
            best_sequence.insert(0, first_missile_id)
            
            # 重新计算评分
            temp_env = MissileLaunchEnvironment(n_missiles, tdistance_path=tdistance_path)
            for missile_id in best_sequence:
                for i, missile in enumerate(temp_env.unfired_missiles):
                    if missile[0] == missile_id:
                        temp_env.step(i)
                        break
            best_score = temp_env.get_final_score()
    
    # 验证发射序列的有效性，使用严格标准（行距离>=3）
    valid_sequence = True
    validation_env = MissileLaunchEnvironment(n_missiles, tdistance_path=tdistance_path)
    if first_missile_id is not None:
        validation_env.reset(first_missile_id)
    
    last_missile_id = None
    invalid_positions = []
    
    # 首先进行严格标准检查
    for i in range(len(best_sequence)):
        # 跳过首发导弹（已经发射）
        if i == 0 and first_missile_id is not None:
            last_missile_id = first_missile_id
            continue
            
        missile_id = best_sequence[i]
        
        # 找到该导弹在未发射列表中的位置
        idx = None
        for j, missile in enumerate(validation_env.unfired_missiles):
            if missile[0] == missile_id:
                idx = j
                break
        
        if idx is None:
            print(f"错误：无法找到导弹ID {missile_id}，可能已经被发射")
            valid_sequence = False
            break
        
        # 检查该导弹是否符合严格发射条件
        missile = validation_env.unfired_missiles[idx]
        is_valid, reason = validation_env.is_valid_next_missile(missile, min_row_distance=3)
        
        if not is_valid:
            print(f"警告：导弹 {missile_id} 在导弹 {last_missile_id} 之后发射不满足严格条件，原因: {reason}")
            invalid_positions.append((i, missile_id, last_missile_id, reason))
        
        # 无论是否有效，都发射该导弹（在验证阶段）
        validation_env.step(idx)
        last_missile_id = missile_id
    
    # 如果存在不满足严格标准的导弹，使用降低的标准重新检查
    if invalid_positions:
        print("存在不满足严格标准的导弹，使用降低的标准重新验证...")
        
        # 重置验证环境
        validation_env = MissileLaunchEnvironment(n_missiles, tdistance_path=tdistance_path)
        if first_missile_id is not None:
            validation_env.reset(first_missile_id)
            
        relaxed_valid = True
        last_missile_id = None
        
        for i in range(len(best_sequence)):
            # 跳过首发导弹
            if i == 0 and first_missile_id is not None:
                last_missile_id = first_missile_id
                continue
                
            missile_id = best_sequence[i]
            
            # 找到该导弹在未发射列表中的位置
            idx = None
            for j, missile in enumerate(validation_env.unfired_missiles):
                if missile[0] == missile_id:
                    idx = j
                    break
            
            if idx is None:
                relaxed_valid = False
                break
            
            # 使用降低的标准检查
            missile = validation_env.unfired_missiles[idx]
            is_valid, reason = validation_env.is_valid_next_missile(missile, min_row_distance=2)
            
            if not is_valid:
                print(f"警告：导弹 {missile_id} 在导弹 {last_missile_id} 之后发射即使在降低标准后仍不符合规则，原因: {reason}")
                relaxed_valid = False
                break
            
            # 发射该导弹
            validation_env.step(idx)
            last_missile_id = missile_id
        
        if relaxed_valid:
            print("在降低标准后，序列符合要求")
            # 使用降低标准后的评分
            best_score = validation_env.get_final_score()
        else:
            print("警告：即使在降低标准后，序列仍然不符合规则")
            # 保留原序列，不做更改
    
    # 返回最优序列和评分
    return best_sequence, best_score

def run_dqn_for_ui(tdistance_path, n_missiles, first_missile_id=None):
    """
    为UI界面提供的DQN算法接口函数
    
    参数:
    - tdistance_path: Tdistance.xlsx文件的路径
    - n_missiles: 导弹数量（界面上的列数×2）
    - first_missile_id: 首发导弹ID（可选）
    
    返回:
    - best_sequence: 最优发射序列
    - best_score: 序列评分
    """
    # 重置打印标志，确保可以看到距离数据（可选）
    MissileLaunchEnvironment._distance_data_printed = False
    
    try:
        print(f"\n开始计算导弹发射顺序优化 (导弹数量: {n_missiles})")
        print(f"使用距离数据文件: {tdistance_path}")
        
        if n_missiles not in [4, 8, 12, 16, 32]:
            print(f"警告: 导弹数量必须为4, 8, 12, 16或32，当前值为{n_missiles}")
            n_missiles = min([x for x in [4, 8, 12, 16, 32] if x >= n_missiles], default=32)
            print(f"已调整为最接近的有效值: {n_missiles}")
        
        # 使用新训练的模型（不加载预训练模型）
        start_time = time.time()
        sequence, score = find_optimal_sequence_rl(n_missiles, first_missile_id, train_mode=True, tdistance_path=tdistance_path)
        end_time = time.time()
        
        # 打印结果
        print("\n" + "=" * 60)
        print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
        print("最优发射序列:")
        print(sequence)
        print(f"序列评分: {score:.4f}")
        print("=" * 60 + "\n")
        
        return sequence, score
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return [], 0.0

def calculate_optimal_sequence_for_qt(tdistance_path, columns_count, first_missile_id=None):
    """
    专为Qt界面设计的接口函数，在确定按钮点击时调用
    
    参数:
    - tdistance_path: Tdistance.xlsx文件路径
    - columns_count: 界面上设置的列数
    - first_missile_id: 首发导弹ID (可选)
    
    返回:
    - sequence: 最优发射序列
    - score: 序列评分
    """
    # 计算实际导弹数量 (列数 * 2)
    n_missiles = columns_count * 2
    
    # 调用DQN算法计算最优序列
    return run_dqn_for_ui(tdistance_path, n_missiles, first_missile_id)

# 测试导出函数，供Qt界面调用
def get_optimal_sequence(tdistance_path, columns_count, first_missile_id=None):
    """
    导出函数，供Qt界面直接导入使用
    
    示例用法:
    from missile_launch_rl import get_optimal_sequence
    
    # 在Qt界面中的确定按钮点击事件中:
    def on_confirm_button_clicked(self):
        tdistance_path = "path/to/Tdistance.xlsx"
        columns_count = self.columnsSpinBox.value()  # 假设界面有个spinbox设置列数
        first_missile = self.firstMissileSpinBox.value() if self.useFirstMissileCheckBox.isChecked() else None
        
        sequence, score = get_optimal_sequence(tdistance_path, columns_count, first_missile)
        
        # 处理结果...
        print(f"最优发射序列: {sequence}")
        print(f"序列评分: {score:.4f}")
    """
    return calculate_optimal_sequence_for_qt(tdistance_path, columns_count, first_missile_id)

def main():
    print("欢迎使用基于强化学习的潜艇导弹发射顺序优化程序")
    print("=" * 60)
    
    while True:
        try:
            # 获取用户输入
            n_missiles = int(input("请输入导弹数量 (4, 8, 12, 16, 32): "))
            if n_missiles not in [4, 8, 12, 16, 32]:
                print("错误: 导弹数量必须为4, 8, 12, 16或32")
                continue
                
            # 获取首发导弹ID
            first_missile_choice = input(f"请输入首发导弹编号 (1-{n_missiles}，可选，按Enter跳过): ")
            first_missile_id = int(first_missile_choice) if first_missile_choice.strip() else None
            
            if first_missile_id is not None and (first_missile_id < 1 or first_missile_id > n_missiles):
                print(f"错误: 首发导弹编号必须在1到{n_missiles}之间")
                continue
                
            # 选择模式
            mode_choice = input("选择模式: 1.训练新模型 2.使用预训练模型 [2]: ") or "2"
            train_mode = (mode_choice == "1")
            
            if not train_mode:
                model_path = f"missile_dqn_model_{n_missiles}.pth"
                if not os.path.exists(model_path):
                    print(f"警告: 没有找到导弹数量为{n_missiles}的预训练模型文件")
                    print(f"将使用未训练模型或尝试使用备选模型")
                else:
                    print(f"找到导弹数量为{n_missiles}的预训练模型")
            
            # 运行算法
            sequence, score = find_optimal_sequence_rl(n_missiles, first_missile_id, train_mode)
            
            # 打印结果
            print("\n" + "=" * 60)
            print("最优发射序列:")
            print(sequence)
            print(f"序列评分: {score:.4f}")
            print("=" * 60)
            
            # 是否继续
            continue_choice = input("是否继续优化其他配置? (y/n): ").lower()
            if continue_choice != 'y':
                break
                
        except ValueError:
            print("输入错误: 请输入有效的数字")
        except Exception as e:
            print(f"发生错误: {e}")

# 如果直接运行此文件，则执行主函数
if __name__ == "__main__":
    # 检查命令行参数，判断是否由UI调用
    if len(sys.argv) > 2 and sys.argv[1] == "--from-ui":
        # 从UI调用，格式：python missile_launch_rl.py --from-ui <tdistance_path> <n_missiles> [first_missile_id]
        tdistance_path = sys.argv[2]
        n_missiles = int(sys.argv[3])
        first_missile_id = int(sys.argv[4]) if len(sys.argv) > 4 else None
        
        run_dqn_for_ui(tdistance_path, n_missiles, first_missile_id)
    else:
        # 普通命令行运行
    main() 