import pandas as pd
from datetime import datetime
import os


class DDManager:
    def __init__(self):
        self.selected_dds = []
        self.scores = {}
        # 设置默认保存目录为当前目录下的data文件夹
        self.save_dir = os.path.join(os.getcwd(), "data")

    def setSelectedDDs(self, dds):
        self.selected_dds = dds

    def getSelectedDDs(self):
        return self.selected_dds

    def setScores(self, scores):
        self.scores = scores

    def saveToExcel(self):
        """保存数据到Excel并返回保存路径，失败返回None"""
        if not self.scores:
            return None

        # 确保保存目录存在
        os.makedirs(self.save_dir, exist_ok=True)

        # 创建数据框
        data = []
        for dd_id in sorted(self.scores.keys()):
            status = "是" if dd_id in self.selected_dds else "不是"
            score = self.scores.get(dd_id, 0)
            data.append({
                "DD编号": dd_id,
                "选择状态": status,
                "健康分": score
            })

        df = pd.DataFrame(data)

        # 保存到Excel
        filename = f"DD健康分_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path = os.path.join(self.save_dir, filename)

        try:
            df.to_excel(file_path, index=False)
            return file_path
        except Exception as e:
            print(f"保存Excel失败: {e}")
            return None