from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                             QLabel, QRadioButton, QButtonGroup, QDialogButtonBox)


class PriorityDialog(QDialog):
    def __init__(self, parent=None, option_count=0):
        super().__init__(parent)
        self.setWindowTitle('设置DD发射优先级')
        self.resize(500, 400)
        self.option_count = option_count
        self.priorities = {}  # {option_id: priority}
        self.selected_priorities = set()  # 已被选择的优先级

        self.setupUI()

    def setupUI(self):
        layout = QVBoxLayout(self)

        if self.option_count <= 0:
            layout.addWidget(QLabel("请输入有效的选项数量", self))
            return

        # 为每个选项创建一个单选按钮组
        self.option_groups = {}  # 存储每个选项的按钮组
        self.radio_buttons = {}  # 存储所有单选按钮，按 (option_id, priority) 索引

        for i in range(self.option_count):
            option_id = i + 1
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(f"DD编号{option_id}:", self))

            btn_group = QButtonGroup(self)
            self.option_groups[option_id] = btn_group

            # 为每个选项创建指定数量的单选按钮
            for priority in range(1, self.option_count + 1):
                radio = QRadioButton(str(priority), self)
                radio.setProperty("option_id", option_id)
                radio.setProperty("priority", priority)

                # 保存按钮引用，以便后续禁用/启用
                self.radio_buttons[(option_id, priority)] = radio

                # 连接信号
                radio.toggled.connect(self.on_radio_toggled)
                btn_group.addButton(radio, priority)
                row_layout.addWidget(radio)

            layout.addLayout(row_layout)

        # 添加确认和取消按钮
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        ok_button = btn_box.button(QDialogButtonBox.Ok)
        cancel_button = btn_box.button(QDialogButtonBox.Cancel)
        ok_button.setText("确定")
        cancel_button.setText("取消")

        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def on_radio_toggled(self, checked):
        radio = self.sender()
        if not checked:
            return  # 只处理选中状态

        option_id = radio.property("option_id")
        priority = radio.property("priority")

        # 更新优先级选择
        self.priorities[option_id] = priority

        # 更新所有按钮状态
        self.update_radio_states()

    def update_radio_states(self):
        """更新所有单选按钮的启用/禁用状态"""
        # 收集当前已选择的优先级
        self.selected_priorities = set(self.priorities.values())

        # 更新每个单选按钮的状态
        for (option_id, priority), radio in self.radio_buttons.items():
            # 如果该优先级已被其他选项选择，则禁用
            if priority in self.selected_priorities and priority != self.priorities.get(option_id):
                radio.setEnabled(False)
            else:
                radio.setEnabled(True)

    def getPriorities(self):
        """返回所有设置的优先级，未选择的选项不会出现在字典中"""
        return self.priorities