#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
邻桶效应图表演示程序
展示不同导弹发射序列的邻桶效应变化
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from neighbor_effect_chart import NeighborEffectChart

class NeighborEffectDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("邻桶效应图表演示 - 导弹发射序列优化")
        self.setGeometry(100, 100, 900, 700)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton {
                background-color: #004293;
                color: white;
                border: 1px solid #00FFFF;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0066cc;
            }
            QPushButton:pressed {
                background-color: #003366;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title_label = QLabel("邻桶效应可视化演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; color: #00FFFF; font-weight: bold; padding: 20px;")
        main_layout.addWidget(title_label)
        
        # 添加说明
        desc_label = QLabel("邻桶效应表示剩余导弹之间的相互干扰程度。随着导弹的逐步发射，剩余导弹数量减少，干扰效应逐渐降低。")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("font-size: 12px; color: white; padding: 10px;")
        desc_label.setWordWrap(True)
        main_layout.addWidget(desc_label)
        
        # 创建邻桶效应图表
        self.neighbor_chart = NeighborEffectChart()
        main_layout.addWidget(self.neighbor_chart)
        
        # 创建控制按钮区域
        control_layout = QVBoxLayout()
        
        # 第一行按钮：不同导弹数量的测试
        row1_layout = QHBoxLayout()
        row1_label = QLabel("不同导弹数量测试:")
        row1_label.setStyleSheet("font-size: 12px; color: #00FFFF;")
        row1_layout.addWidget(row1_label)
        
        test_cases_1 = [
            ("4发导弹", [1, 3, 2, 4], 4),
            ("8发导弹", [1, 3, 5, 7, 2, 4, 6, 8], 8),
            ("16发导弹", [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16], 16),
            ("32发导弹", list(range(1, 33, 2)) + list(range(2, 33, 2)), 32)
        ]
        
        for name, sequence, count in test_cases_1:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, s=sequence, c=count, n=name: self.update_chart(s, c, n))
            row1_layout.addWidget(btn)
        
        control_layout.addLayout(row1_layout)
        
        # 第二行按钮：不同发射策略测试
        row2_layout = QHBoxLayout()
        row2_label = QLabel("不同发射策略测试:")
        row2_label.setStyleSheet("font-size: 12px; color: #00FFFF;")
        row2_layout.addWidget(row2_label)
        
        test_cases_2 = [
            ("顺序发射", [1, 2, 3, 4, 5, 6, 7, 8], 8),
            ("交替发射", [1, 3, 5, 7, 2, 4, 6, 8], 8),
            ("随机发射", [3, 1, 6, 2, 8, 4, 7, 5], 8),
            ("清空图表", [], 0)
        ]
        
        for name, sequence, count in test_cases_2:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, s=sequence, c=count, n=name: self.update_chart(s, c, n))
            row2_layout.addWidget(btn)
        
        control_layout.addLayout(row2_layout)
        
        # 第三行：动态演示按钮
        row3_layout = QHBoxLayout()
        row3_label = QLabel("动态演示:")
        row3_label.setStyleSheet("font-size: 12px; color: #00FFFF;")
        row3_layout.addWidget(row3_label)
        
        self.demo_btn = QPushButton("开始动态演示")
        self.demo_btn.clicked.connect(self.start_dynamic_demo)
        row3_layout.addWidget(self.demo_btn)
        
        self.stop_btn = QPushButton("停止演示")
        self.stop_btn.clicked.connect(self.stop_dynamic_demo)
        self.stop_btn.setEnabled(False)
        row3_layout.addWidget(self.stop_btn)
        
        control_layout.addLayout(row3_layout)
        
        main_layout.addLayout(control_layout)
        
        # 状态显示
        self.status_label = QLabel("准备就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 12px; color: #00FFFF; padding: 10px;")
        main_layout.addWidget(self.status_label)
        
        # 动态演示相关
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.update_dynamic_demo)
        self.demo_sequence = [1, 3, 5, 7, 9, 11, 13, 15, 2, 4, 6, 8, 10, 12, 14, 16]
        self.demo_step = 0
        
        # 初始显示
        self.update_chart([1, 3, 2, 4], 4, "4发导弹示例")
    
    def update_chart(self, sequence, missile_count, name):
        """更新图表"""
        self.neighbor_chart.update_chart(sequence, missile_count)
        self.status_label.setText(f"当前显示: {name} - 序列: {sequence[:8]}{'...' if len(sequence) > 8 else ''}")
    
    def start_dynamic_demo(self):
        """开始动态演示"""
        self.demo_step = 0
        self.demo_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("动态演示进行中...")
        self.demo_timer.start(1000)  # 每秒更新一次
    
    def stop_dynamic_demo(self):
        """停止动态演示"""
        self.demo_timer.stop()
        self.demo_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("动态演示已停止")
    
    def update_dynamic_demo(self):
        """更新动态演示"""
        if self.demo_step <= len(self.demo_sequence):
            current_sequence = self.demo_sequence[:self.demo_step]
            self.neighbor_chart.update_chart(current_sequence, len(self.demo_sequence))
            self.status_label.setText(f"动态演示: 已发射 {self.demo_step}/{len(self.demo_sequence)} 发导弹")
            self.demo_step += 1
        else:
            self.stop_dynamic_demo()

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = NeighborEffectDemo()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
