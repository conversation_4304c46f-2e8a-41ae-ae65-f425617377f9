import numpy as np
import multiprocessing as mp
import time
import pandas as pd
from tqdm import tqdm
import os
import pickle
from typing import Dict, List, Tuple, Set, Any, Optional, Union

class MissileLaunchW5Generator:
    """基于W=3数据集高效生成W=5数据集的类"""
    
    def __init__(self, w3_evaluator=None, num_processes=None, top_percent=0.2, 
                alpha1=0.35, alpha2=0.30, alpha3=0.35):
        """初始化W=5数据集生成器
        
        Args:
            w3_evaluator: W=3评估器实例，用于获取导弹位置信息
            num_processes: 并行处理的进程数，默认使用CPU核心数
            top_percent: 选择前多少比例的W=3组合，默认0.2（20%）
            alpha1: 第一个W=3子序列权重，默认0.35
            alpha2: 中间W=3子序列权重，默认0.30
            alpha3: 最后一个W=3子序列权重，默认0.35
        """
        self.w3_evaluator = w3_evaluator
        self.num_processes = num_processes if num_processes else mp.cpu_count()
        
        # 索引结构
        self.pair_to_third = {}  # (a,b) -> [(c1,score1), (c2,score2), ...]
        self.overlap_index = {}  # (b,c) -> [(a,'prev',score_abc), (d,'next',score_bcd), ...]
        
        # 高分W=3组合集合
        self.top_w3_combinations = []
        
        # 参数设置 - 现在从构造函数参数获取
        self.top_percent = top_percent  # 选择前多少%的W=3组合
        self.alpha1 = alpha1  # 第一个W=3子序列权重
        self.alpha2 = alpha2  # 中间W=3子序列权重
        self.alpha3 = alpha3  # 最后一个W=3子序列权重
        
    def build_indices(self, w3_results):
        """构建索引结构
        
        Args:
            w3_results: W=3评分结果列表，格式为[(tuple(a,b,c), score), ...]
        """
        print("构建W=5索引结构...")
        start_time = time.time()
        
        # 初始化索引
        self.pair_to_third = {}
        self.overlap_index = {}
        
        # 填充索引
        for (a, b, c), score in tqdm(w3_results):
            # 更新pair_to_third索引
            if (a, b) not in self.pair_to_third:
                self.pair_to_third[(a, b)] = []
            self.pair_to_third[(a, b)].append((c, score))
            
            # 更新overlap_index索引
            # 记录a可以放在(b,c)前面
            if (b, c) not in self.overlap_index:
                self.overlap_index[(b, c)] = []
            self.overlap_index[(b, c)].append((a, 'prev', score))
            
            # 记录c可以放在(a,b)后面
            if (a, b) not in self.overlap_index:
                self.overlap_index[(a, b)] = []
            self.overlap_index[(a, b)].append((c, 'next', score))
        
        # 对索引条目按评分排序
        for key in self.pair_to_third:
            self.pair_to_third[key].sort(key=lambda x: x[1], reverse=True)
            
        for key in self.overlap_index:
            self.overlap_index[key].sort(key=lambda x: x[2], reverse=True)
        
        elapsed_time = time.time() - start_time
        print(f"索引构建完成，耗时: {elapsed_time:.2f}秒")
        
        # 分析索引统计信息
        num_pair_keys = len(self.pair_to_third)
        num_overlap_keys = len(self.overlap_index)
        avg_candidates = sum(len(v) for v in self.pair_to_third.values()) / max(1, num_pair_keys)
        
        print(f"索引统计: pair_to_third键数量: {num_pair_keys}, "
              f"overlap_index键数量: {num_overlap_keys}, "
              f"平均候选数: {avg_candidates:.2f}")
        
    def select_top_w3_combinations(self, w3_results):
        """选择高分W=3组合
        
        Args:
            w3_results: W=3评分结果列表
            
        Returns:
            高分W=3组合列表
        """
        # 按评分排序
        sorted_w3 = sorted(w3_results, key=lambda x: x[1], reverse=True)
        
        # 选择前top_percent的组合
        top_count = int(len(sorted_w3) * self.top_percent)
        self.top_w3_combinations = sorted_w3[:top_count]
        
        print(f"选择了前{self.top_percent*100}%的W=3组合，共{len(self.top_w3_combinations)}个")
        
        return self.top_w3_combinations
    
    def analyze_overlap_quality(self):
        """分析重叠对的质量
        
        Returns:
            重叠对质量字典，格式为{(b,c): quality_score, ...}
        """
        overlap_quality = {}
        
        # 统计每个重叠对在高分组合中出现的次数
        for (a, b, c), score in self.top_w3_combinations:
            if (b, c) not in overlap_quality:
                overlap_quality[(b, c)] = {'count': 0, 'scores': []}
            overlap_quality[(b, c)]['count'] += 1
            overlap_quality[(b, c)]['scores'].append(score)
        
        # 计算质量分数 = 出现次数 * 平均分数
        for key in overlap_quality:
            count = overlap_quality[key]['count']
            avg_score = sum(overlap_quality[key]['scores']) / count
            overlap_quality[key] = count * avg_score
        
        # 按质量排序
        sorted_overlap = sorted(overlap_quality.items(), key=lambda x: x[1], reverse=True)
        
        print(f"识别了{len(sorted_overlap)}个重叠对，按质量排序")
        
        return dict(sorted_overlap)
    
    def compute_w5_score(self, score_abc, score_bcd, score_cde):
        """计算W=5评分
        
        Args:
            score_abc: 第一个W=3子序列的评分
            score_bcd: 第二个W=3子序列的评分
            score_cde: 第三个W=3子序列的评分
            
        Returns:
            W=5组合的评分
        """
        # 加权平均
        return (self.alpha1 * score_abc + 
                self.alpha2 * score_bcd + 
                self.alpha3 * score_cde)
    
    def process_batch(self, batch_id, w3_batch, w3_dict):
        """处理一批W=3组合，生成W=5组合
        
        Args:
            batch_id: 批次ID
            w3_batch: 要处理的W=3组合批次
            w3_dict: W=3评分字典，用于快速查找
            
        Returns:
            生成的W=5组合及评分列表
        """
        results = []
        
        for i, ((a, b, c), score_abc) in enumerate(w3_batch):
            # 查找可能的第四枚导弹d
            if (b, c) not in self.overlap_index:
                continue
                
            for d, type_d, score_bcd in self.overlap_index[(b, c)]:
                # 只考虑可以作为后续的导弹
                if type_d != 'next' or d in {a, b, c}:
                    continue
                    
                # 查找可能的第五枚导弹e
                if (c, d) not in self.overlap_index:
                    continue
                    
                for e, type_e, score_cde in self.overlap_index[(c, d)]:
                    # 只考虑可以作为后续的导弹
                    if type_e != 'next' or e in {a, b, c, d}:
                        continue
                        
                    # 计算W=5评分
                    w5_score = self.compute_w5_score(score_abc, score_bcd, score_cde)
                    results.append(((a, b, c, d, e), w5_score))
        
        return results
    
    def generate_w5_dataset(self, w3_results):
        """生成W=5数据集
        
        Args:
            w3_results: W=3评分结果列表
            
        Returns:
            W=5组合及评分列表
        """
        start_time = time.time()
        
        # 构建索引
        self.build_indices(w3_results)
        
        # 选择高分W=3组合
        self.select_top_w3_combinations(w3_results)
        
        # 分析重叠质量
        overlap_quality = self.analyze_overlap_quality()
        
        # 准备并行处理
        print(f"使用{self.num_processes}个进程并行生成W=5数据集...")
        
        # 划分批次
        batch_size = len(self.top_w3_combinations) // self.num_processes
        batches = []
        for i in range(self.num_processes):
            start_idx = i * batch_size
            end_idx = start_idx + batch_size if i < self.num_processes-1 else len(self.top_w3_combinations)
            batches.append((i, self.top_w3_combinations[start_idx:end_idx], {(a, b, c): score for (a, b, c), score in w3_results}))
        
        # 并行处理
        with mp.Pool(processes=self.num_processes) as pool:
            all_results = pool.starmap(self.process_batch, batches)
        
        # 合并结果
        w5_results = []
        for batch_result in all_results:
            w5_results.extend(batch_result)
        
        # 按评分排序
        w5_results.sort(key=lambda x: x[1], reverse=True)
        
        elapsed_time = time.time() - start_time
        print(f"W=5数据集生成完成，共{len(w5_results)}个组合，耗时: {elapsed_time:.2f}秒")
        
        return w5_results
    
    def save_to_excel(self, w5_results, output_file="missile_launch_w5_scores.xlsx"):
        """将W=5评分结果保存到Excel文件
        
        Args:
            w5_results: W=5评分结果列表
            output_file: 输出Excel文件路径
        """
        # 创建数据框
        data = {
            "发射顺序": ["-".join(str(m) for m in group) for (group, _) in w5_results],
            "评分": [score for _, score in w5_results]
        }
        df = pd.DataFrame(data)
        
        # 检查数据量大小
        row_count = len(df)
        excel_max_rows = 1048576  # Excel最大行数限制
        
        # 如果数据量超过Excel限制，改为保存为CSV文件
        if row_count > excel_max_rows:
            csv_file = output_file.replace('.xlsx', '.csv')
            df.to_csv(csv_file, index=False)
            print(f"数据量过大({row_count}行)，超出Excel限制(1048576行)，已保存为CSV文件: {csv_file}")
        else:
            # 保存到Excel
            df.to_excel(output_file, index=False)
            print(f"W=5结果已保存到 {output_file}")
    
    def analyze_w5_results(self, w5_results, top_n=10):
        """分析W=5结果
        
        Args:
            w5_results: W=5评分结果列表
            top_n: 显示前多少个高分组合
            
        Returns:
            分析结果字典
        """
        # 显示前top_n个高分组合
        print(f"\nW=5评分最高的前{top_n}个组合:")
        for i, ((a, b, c, d, e), score) in enumerate(w5_results[:top_n]):
            print(f"{i+1}. {a}-{b}-{c}-{d}-{e}, 评分: {score:.4f}")
        
        # 统计评分分布
        scores = [score for _, score in w5_results]
        stats = {
            "count": len(scores),
            "min": min(scores),
            "max": max(scores),
            "mean": sum(scores) / len(scores),
            "median": sorted(scores)[len(scores)//2],
            "std": np.std(scores)
        }
        
        print(f"\nW=5评分统计:")
        print(f"数量: {stats['count']}")
        print(f"最低分: {stats['min']:.4f}")
        print(f"最高分: {stats['max']:.4f}")
        print(f"平均分: {stats['mean']:.4f}")
        print(f"中位数: {stats['median']:.4f}")
        print(f"标准差: {stats['std']:.4f}")
        
        return stats


def generate_w5_dataset_from_w3(w3_results, w3_evaluator=None, num_processes=None,
                              top_percent=0.2, alpha1=0.35, alpha2=0.30, alpha3=0.35):
    """从W=3数据集生成W=5数据集的便捷函数
    
    Args:
        w3_results: W=3评分结果列表
        w3_evaluator: W=3评估器实例
        num_processes: 并行处理的进程数
        top_percent: 选择前多少比例的W=3组合
        alpha1: 第一个W=3子序列权重
        alpha2: 中间W=3子序列权重
        alpha3: 最后一个W=3子序列权重
        
    Returns:
        W=5组合及评分列表
    """
    generator = MissileLaunchW5Generator(
        w3_evaluator, num_processes, top_percent, alpha1, alpha2, alpha3)
    w5_results = generator.generate_w5_dataset(w3_results)
    return w5_results


# 示例用法（如果直接运行此脚本）
if __name__ == "__main__":
    import sys
    sys.path.append('.')  # 确保能导入同目录下的模块
    
    try:
        from missile_launch_evaluator import MissileLaunchEvaluator
    except ImportError:
        print("无法导入MissileLaunchEvaluator，请确保missile_launch_evaluator.py在当前目录")
        sys.exit(1)
    
    # 创建W=3评估器
    n = 16  # 导弹数量
    evaluator = MissileLaunchEvaluator(num_missiles=n)
    
    # 计算W=3组合或从文件加载
    w3_file = f"missile_launch_w3_scores_n{n}.xlsx"
    if os.path.exists(w3_file):
        print(f"从文件加载W=3数据集: {w3_file}")
        df = pd.read_excel(w3_file)
        w3_results = []
        for _, row in df.iterrows():
            sequence = tuple(int(m) for m in row['发射顺序'].split('-'))
            score = row['评分']
            w3_results.append((sequence, score))
    else:
        print("计算W=3数据集...")
        w3_results = evaluator.compute_all_combinations()
        evaluator.save_to_excel(w3_results, w3_file)
    
    # 用户输入W=5参数
    print("\n请输入W=5生成算法参数（直接回车使用默认值）：")
    
    # 获取top_percent
    top_percent_str = input(f"选择前多少比例的W=3组合 (0-1之间，默认0.2): ").strip()
    top_percent = 0.2
    if top_percent_str:
        try:
            top_percent = float(top_percent_str)
            if top_percent <= 0 or top_percent > 1:
                print("比例必须在0-1之间，使用默认值0.2")
                top_percent = 0.2
        except ValueError:
            print("输入无效，使用默认值0.2")
    
    # 获取alpha参数
    alpha1_str = input(f"第一个W=3子序列权重 (默认0.35): ").strip()
    alpha1 = 0.35
    if alpha1_str:
        try:
            alpha1 = float(alpha1_str)
        except ValueError:
            print("输入无效，使用默认值0.35")
            
    alpha2_str = input(f"中间W=3子序列权重 (默认0.30): ").strip()
    alpha2 = 0.30
    if alpha2_str:
        try:
            alpha2 = float(alpha2_str)
        except ValueError:
            print("输入无效，使用默认值0.30")
    
    alpha3_str = input(f"最后一个W=3子序列权重 (默认0.35): ").strip()
    alpha3 = 0.35
    if alpha3_str:
        try:
            alpha3 = float(alpha3_str)
        except ValueError:
            print("输入无效，使用默认值0.35")
    
    # 检查权重总和是否为1.0
    total_weight = alpha1 + alpha2 + alpha3
    if abs(total_weight - 1.0) > 0.001:
        print(f"警告：权重总和为{total_weight}，不为1.0，将进行归一化处理")
        factor = 1.0 / total_weight
        alpha1 *= factor
        alpha2 *= factor
        alpha3 *= factor
        print(f"归一化后的权重：{alpha1:.3f}, {alpha2:.3f}, {alpha3:.3f}")
    
    # 确认进程数
    num_processes_str = input(f"并行处理的进程数 (默认使用CPU核心数{mp.cpu_count()}): ").strip()
    num_processes = mp.cpu_count()
    if num_processes_str:
        try:
            num_processes = int(num_processes_str)
            if num_processes < 1:
                print(f"进程数必须大于0，使用默认值{mp.cpu_count()}")
                num_processes = mp.cpu_count()
        except ValueError:
            print(f"输入无效，使用默认值{mp.cpu_count()}")
    
    # 生成W=5数据集
    print(f"\n使用参数：top_percent={top_percent}, alpha1={alpha1:.3f}, alpha2={alpha2:.3f}, alpha3={alpha3:.3f}, num_processes={num_processes}")
    
    w5_results = generate_w5_dataset_from_w3(
        w3_results, evaluator, num_processes, 
        top_percent, alpha1, alpha2, alpha3)
    
    # 保存W=5结果到Excel
    w5_file = f"missile_launch_w5_scores_n{n}.xlsx"
    generator = MissileLaunchW5Generator(evaluator, num_processes, top_percent, alpha1, alpha2, alpha3)
    generator.save_to_excel(w5_results, w5_file)
    
    # 分析结果
    generator.analyze_w5_results(w5_results, top_n=10) 