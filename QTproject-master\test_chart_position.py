#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邻桶效应图表位置是否正确
验证图表是否完全在界面范围内
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QLabel
from PyQt5.QtCore import Qt
from neighbor_effect_chart import NeighborEffectChart

class PositionTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("邻桶效应图表位置测试")
        # 设置与主界面相同的尺寸
        self.setGeometry(100, 100, 1325, 852)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                border: 1px solid #00FFFF;
                padding: 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标题
        title_label = QLabel("邻桶效应图表位置测试 - 验证图表是否完全在界面范围内")
        title_label.setGeometry(10, 10, 1305, 30)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setParent(central_widget)
        
        # 模拟左侧控制区域 (0-650)
        left_area = QLabel("左侧控制区域\n(0-650像素)")
        left_area.setGeometry(10, 50, 630, 350)
        left_area.setAlignment(Qt.AlignCenter)
        left_area.setStyleSheet("background-color: rgba(0, 20, 40, 0.3); border: 2px dashed #00FFFF;")
        left_area.setParent(central_widget)
        
        # 模拟groupBox结果区域 (650-950)
        groupbox_area = QLabel("DD FS顺序优化结果区域\n(650-950像素)\n宽度: 300像素")
        groupbox_area.setGeometry(650, 50, 300, 350)
        groupbox_area.setAlignment(Qt.AlignCenter)
        groupbox_area.setStyleSheet("background-color: rgba(0, 34, 68, 0.7); border: 2px solid #00FFFF;")
        groupbox_area.setParent(central_widget)
        
        # 创建邻桶效应图表 (960-1310)
        self.neighbor_chart = NeighborEffectChart()
        # 使用与main.py相同的位置计算
        chart_x = 650 + 300 + 10  # = 960
        chart_width = 1325 - chart_x - 15  # = 350
        chart_width = min(chart_width, 350)
        self.neighbor_chart.setGeometry(chart_x, 50, chart_width, 350)
        self.neighbor_chart.setParent(central_widget)
        
        # 显示图表位置信息
        chart_info = QLabel(f"邻桶效应图表区域\n位置: ({chart_x}, 50)\n尺寸: ({chart_width}, 350)\n右边界: {chart_x + chart_width}")
        chart_info.setGeometry(chart_x, 410, chart_width, 80)
        chart_info.setAlignment(Qt.AlignCenter)
        chart_info.setStyleSheet("background-color: rgba(0, 68, 34, 0.7); border: 2px solid #00FF00;")
        chart_info.setParent(central_widget)
        
        # 显示界面边界信息
        boundary_info = QLabel(f"界面总宽度: 1325像素\n图表右边界: {chart_x + chart_width}像素\n剩余空间: {1325 - (chart_x + chart_width)}像素")
        boundary_info.setGeometry(10, 420, 630, 80)
        boundary_info.setAlignment(Qt.AlignCenter)
        if chart_x + chart_width <= 1325:
            boundary_info.setStyleSheet("background-color: rgba(0, 68, 0, 0.7); border: 2px solid #00FF00; color: #00FF00;")
            boundary_info.setText(boundary_info.text() + "\n✅ 图表完全在界面范围内")
        else:
            boundary_info.setStyleSheet("background-color: rgba(68, 0, 0, 0.7); border: 2px solid #FF0000; color: #FF0000;")
            boundary_info.setText(boundary_info.text() + "\n❌ 图表超出界面范围")
        boundary_info.setParent(central_widget)
        
        # 添加测试数据到图表
        test_sequence = [1, 3, 5, 7, 2, 4, 6, 8]
        self.neighbor_chart.update_chart(test_sequence, 8)
        
        # 显示详细计算过程
        calc_info = QLabel(f"""计算过程:
groupBox位置: (650, 50, 300, 350)
图表X坐标: 650 + 300 + 10 = {chart_x}
图表宽度: 1325 - {chart_x} - 15 = {chart_width}
图表右边界: {chart_x} + {chart_width} = {chart_x + chart_width}
界面宽度: 1325
是否超出: {'否' if chart_x + chart_width <= 1325 else '是'}""")
        calc_info.setGeometry(10, 520, 1305, 120)
        calc_info.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        calc_info.setStyleSheet("background-color: rgba(20, 20, 20, 0.8); border: 1px solid #00FFFF; font-family: monospace;")
        calc_info.setParent(central_widget)

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = PositionTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
